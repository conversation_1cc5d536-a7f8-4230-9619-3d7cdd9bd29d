import api from './api';

export interface QuotaStats {
  douyinAccountCount: number;
  qingliangAccountCount: number;
  douyinStoreCount: number;
  qingliangStoreCount: number;
}

export interface QuotaLimits {
  totalDouyinAccountQuota: number;
  totalQingliangAccountQuota: number;
  totalDouyinStoreQuota: number;
  totalQingliangStoreQuota: number;
}

export interface DashboardData {
  quotaStats: QuotaStats;
  quotaLimits: QuotaLimits;
  isWithinLimit: boolean;
  exceededTypes: string[];
  subUserCount: number;
}

export interface SubUserStats {
  douyinAccountCount: number;
  qingliangAccountCount: number;
  douyinStoreCount: number;
  qingliangStoreCount: number;
  totalAccountCount: number;
  totalStoreCount: number;
}

export interface SubUser {
  id: string;
  username: string;
  phone?: string;
  email?: string;
  company?: string;
  role: string;
  createdAt: string;
  stats: SubUserStats;
  accounts: Array<{
    id: string;
    name: string;
    type: string;
    createdAt: string;
    stores: Array<{
      id: string;
      name: string;
      shortName?: string;
      createdAt: string;
    }>;
  }>;
}

export interface MainAccount {
  id: string;
  username: string;
  phone?: string;
  email?: string;
  company?: string;
  totalDouyinAccountQuota?: number;
  totalQingliangAccountQuota?: number;
  totalDouyinStoreQuota?: number;
  totalQingliangStoreQuota?: number;
  createdAt: string;
}

export interface ImportResult {
  totalSheets: number;
  totalAccounts: number;
  totalStores: number;
  totalAccountsReused: number;
  totalStoresSkipped: number;
  details: Array<{
    sheetName: string;
    cityName: string;
    accountsCreated: number;
    storesCreated: number;
    accountsReused: number;
    storesSkipped: number;
  }>;
}

export interface SubUserWithStats {
  id: string;
  username: string;
  role: string;
  createdAt: string;
  stats: {
    douyinAccountCount: number;
    qingliangAccountCount: number;
    douyinStoreCount: number;
    qingliangStoreCount: number;
    totalAccountCount: number;
    totalStoreCount: number;
  };
}

export interface ClearDataResult {
  message: string;
  clearedData: {
    accounts: number;
    stores: number;
  };
}

export const webService = {
  // 获取仪表板数据
  getDashboard: async (): Promise<DashboardData> => {
    const response = await api.get('/web/dashboard');
    return response.data;
  },

  // 获取子账号列表
  getSubUsers: async (): Promise<SubUser[]> => {
    const response = await api.get('/web/sub-users');
    return response.data;
  },

  // 获取子账号详情
  getSubUserDetails: async (subUserId: string): Promise<SubUser> => {
    const response = await api.get(`/web/sub-users/${subUserId}`);
    return response.data;
  },

  // 获取所有主账号列表（仅超级管理员）
  getMainAccounts: async (): Promise<MainAccount[]> => {
    const response = await api.get('/web/main-accounts');
    return response.data;
  },

  // 获取指定主账号的配额信息（仅超级管理员）
  getMainAccountQuota: async (mainAccountId: string): Promise<DashboardData> => {
    const response = await api.get(`/web/main-account-quota/${mainAccountId}`);
    return response.data;
  },

  // 更新配额设置
  updateQuotaSettings: async (quotaSettings: QuotaLimits & { mainAccountId?: string }): Promise<{ message: string; quotaSettings: QuotaLimits }> => {
    const response = await api.post('/web/quota-settings', quotaSettings);
    return response.data;
  },

  // 为子账号导入抖音数据
  importDouyinForSubUser: async (subUserId: string, file: File, strictMode: boolean = true): Promise<ImportResult> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('strictMode', strictMode.toString());
    
    const response = await api.post(`/web/import/douyin/${subUserId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 为子账号导入清凉数据
  importQingliangForSubUser: async (subUserId: string, file: File, strictMode: boolean = true): Promise<ImportResult> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('strictMode', strictMode.toString());
    
    const response = await api.post(`/web/import/qingliang/${subUserId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 批量导入数据
  batchImportForSubUser: async (subUserId: string, file: File, strictMode: boolean = true): Promise<{ message: string; douyin: ImportResult; qingliang: ImportResult }> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('strictMode', strictMode.toString());
    
    const response = await api.post(`/web/import/batch/${subUserId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // 获取指定主账号下的子账号列表（仅超级管理员）
  getSubUsersByMainAccount: async (mainAccountId: string): Promise<SubUserWithStats[]> => {
    const response = await api.get(`/web/main-account/${mainAccountId}/sub-users`);
    return response.data;
  },

  // 清空指定子账号的数据（仅超级管理员）
  clearSubUserData: async (
    subUserId: string, 
    options: {
      accountType?: 'DOUYIN' | 'QINGLIANG' | 'ALL';
      dataType?: 'accounts' | 'stores' | 'all';
    } = {}
  ): Promise<ClearDataResult> => {
    const response = await api.delete(`/web/clear-data/${subUserId}`, {
      data: options,
    });
    return response.data;
  },
};