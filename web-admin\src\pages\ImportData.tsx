import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Upload,
  Button,
  Space,
  Typography,
  Alert,
  // Radio,
  // Switch,
  Progress,
  Modal,
  Table,
  Tag,
  Spin,
  message,
} from 'antd';
import {
  UploadOutlined,
  ImportOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd/es/upload';
import type { ColumnsType } from 'antd/es/table';
import { webService, type SubUser, type ImportResult } from '../services/web';

const { Title, Text } = Typography;
const { Option } = Select;

interface ImportTask {
  id: string;
  type: 'douyin' | 'qingliang' | 'batch';
  subUserId: string;
  subUserName: string;
  status: 'pending' | 'running' | 'success' | 'error';
  progress: number;
  result?: ImportResult;
  error?: string;
}

const ImportData: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [subUsers, setSubUsers] = useState<SubUser[]>([]);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [importTasks, setImportTasks] = useState<ImportTask[]>([]);
  const [resultModalVisible, setResultModalVisible] = useState(false);
  const [currentResult, setCurrentResult] = useState<ImportResult | null>(null);
  const [errorModalVisible, setErrorModalVisible] = useState(false);
  const [currentError, setCurrentError] = useState<string | null>(null);

  useEffect(() => {
    fetchSubUsers();
  }, []);

  const fetchSubUsers = async () => {
    try {
      setLoading(true);
      const data = await webService.getSubUsers();
      setSubUsers(data);
    } catch (error) {
      console.error('获取子账号列表失败:', error);
      message.error('获取子账号列表失败');
    } finally {
      setLoading(false);
    }
  };

  const uploadProps: UploadProps = {
    beforeUpload: (file) => {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel';
      if (!isExcel) {
        message.error('只支持上传Excel文件（.xlsx或.xls）');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        message.error('文件大小不能超过10MB');
        return false;
      }
      return false; // 阻止自动上传
    },
    onChange: (info) => {
      setFileList(info.fileList.slice(-1)); // 只保留最新的一个文件
    },
    fileList,
    maxCount: 1,
  };

  const addImportTask = (type: ImportTask['type'], subUserId: string): string => {
    const subUser = subUsers.find(user => user.id === subUserId);
    const taskId = Date.now().toString();
    const newTask: ImportTask = {
      id: taskId,
      type,
      subUserId,
      subUserName: subUser?.username || '',
      status: 'pending',
      progress: 0,
    };
    setImportTasks(prev => [...prev, newTask]);
    return taskId;
  };

  const updateTask = (taskId: string, updates: Partial<ImportTask>) => {
    setImportTasks(prev => 
      prev.map(task => 
        task.id === taskId ? { ...task, ...updates } : task
      )
    );
  };

  const executeImport = async (taskId: string, type: ImportTask['type'], subUserId: string, file: File, strictMode: boolean) => {
    updateTask(taskId, { status: 'running', progress: 10 });
    
    try {
      let result;
      updateTask(taskId, { progress: 30 });
      
      switch (type) {
        case 'douyin':
          result = await webService.importDouyinForSubUser(subUserId, file, strictMode);
          break;
        case 'qingliang':
          result = await webService.importQingliangForSubUser(subUserId, file, strictMode);
          break;
        case 'batch':
          const batchResult = await webService.batchImportForSubUser(subUserId, file, strictMode);
          result = batchResult.douyin;
          break;
        default:
          throw new Error('未知的导入类型');
      }
      
      updateTask(taskId, { 
        status: 'success', 
        progress: 100, 
        result: result 
      });
      
      message.success('导入完成');
      
      // 自动弹出结果面板
      if (result) {
        showResult(result);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '导入失败';
      updateTask(taskId, { 
        status: 'error', 
        progress: 0, 
        error: errorMessage 
      });
      message.error(errorMessage);
      
      // 自动弹出错误面板
      showError(errorMessage);
    }
  };

  const handleSubmit = async (values: any) => {
    if (fileList.length === 0) {
      message.error('请选择要导入的Excel文件');
      return;
    }

    const file = fileList[0].originFileObj as File;
    const { subUserId } = values;

    // 默认使用批量导入和严格模式
    const strictMode = true;

    // 批量导入
    const taskId = addImportTask('batch', subUserId);
    await executeImport(taskId, 'batch', subUserId, file, strictMode);
  };

  const showResult = (result: ImportResult) => {
    setCurrentResult(result);
    setResultModalVisible(true);
  };

  const showError = (error: string) => {
    setCurrentError(error);
    setErrorModalVisible(true);
  };

  const taskColumns: ColumnsType<ImportTask> = [
    {
      title: '任务类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          douyin: { text: '抖店导入', color: 'blue' },
          qingliang: { text: '清凉导入', color: 'purple' },
          batch: { text: '批量导入', color: 'green' },
        };
        const config = typeMap[type as keyof typeof typeMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '目标账号',
      dataIndex: 'subUserName',
      key: 'subUserName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        const statusMap = {
          pending: { text: '等待中', color: 'default' },
          running: { text: '进行中', color: 'processing' },
          success: { text: '成功', color: 'success' },
          error: { text: '失败', color: 'error' },
        };
        const config = statusMap[status as keyof typeof statusMap];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number, record: ImportTask) => (
        <Progress
          percent={progress}
          size="small"
          status={record.status === 'error' ? 'exception' : undefined}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {record.result && (
            <Button
              size="small"
              onClick={() => showResult(record.result!)}
            >
              查看结果
            </Button>
          )}
          {record.error && (
            <Button
              size="small"
              danger
              onClick={() => showError(record.error!)}
            >
              查看错误
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const resultColumns: ColumnsType<ImportResult['details'][0]> = [
    { title: 'Sheet名称', dataIndex: 'sheetName', key: 'sheetName' },
    { title: '城市名称', dataIndex: 'cityName', key: 'cityName' },
    { title: '创建账号', dataIndex: 'accountsCreated', key: 'accountsCreated' },
    { title: '复用账号', dataIndex: 'accountsReused', key: 'accountsReused' },
    { title: '创建门店', dataIndex: 'storesCreated', key: 'storesCreated' },
    { title: '跳过门店', dataIndex: 'storesSkipped', key: 'storesSkipped' },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <Title level={2}>数据导入</Title>
      
      <Alert
        message="导入说明"
        description="支持导入Excel文件中的门店和账号数据。系统将使用批量导入模式（同时导入抖音和清凉数据）和严格模式（先清空现有数据再导入）。请确保Excel文件格式正确。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {importTasks.length > 0 && (
        <Card title="导入任务" style={{ marginBottom: 24 }}>
          <Table
            columns={taskColumns}
            dataSource={importTasks}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>
      )}

      <Card title="导入配置" style={{ marginBottom: 24 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{}}
        >
          <Form.Item
            label="选择子账号"
            name="subUserId"
            rules={[{ required: true, message: '请选择要导入数据的子账号' }]}
          >
            <Select placeholder="请选择子账号" showSearch optionFilterProp="children">
              {subUsers.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.username}
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    (抖店: {user.stats.douyinAccountCount}账号/{user.stats.douyinStoreCount}门店,
                     清凉: {user.stats.qingliangAccountCount}账号/{user.stats.qingliangStoreCount}门店)
                  </Text>
                </Option>
              ))}
            </Select>
          </Form.Item>

          {/* 导入类型选择 - 已注释，默认使用批量导入 */}
          {/* <Form.Item
            label="导入类型"
            name="importType"
            rules={[{ required: true, message: '请选择导入类型' }]}
          >
            <Radio.Group>
              <Radio value="batch">批量导入（抖音+清凉）</Radio>
              <Radio value={['douyin']}>仅导入抖音数据</Radio>
              <Radio value={['qingliang']}>仅导入清凉数据</Radio>
              <Radio value={['douyin', 'qingliang']}>分别导入抖音和清凉</Radio>
            </Radio.Group>
          </Form.Item> */}

          {/* 导入模式选择 - 已注释，默认使用严格模式 */}
          {/* <Form.Item
            label="导入模式"
            name="strictMode"
            valuePropName="checked"
          >
            <Switch
              checkedChildren="严格模式"
              unCheckedChildren="追加模式"
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                严格模式：先清空现有数据再导入；追加模式：在现有数据基础上增加
              </Text>
            </div>
          </Form.Item> */}



          <Form.Item label="选择Excel文件">
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>
                选择文件
              </Button>
            </Upload>
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                支持.xlsx和.xls格式，文件大小不超过10MB
              </Text>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              icon={<ImportOutlined />}
              size="large"
              disabled={fileList.length === 0}
            >
              开始导入
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Modal
        title="导入结果详情"
        open={resultModalVisible}
        onCancel={() => setResultModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setResultModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {currentResult && (
          <div>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <Alert
                message={`导入完成`}
                description={`共处理 ${currentResult.totalSheets} 个Sheet，创建 ${currentResult.totalAccounts} 个账号，创建 ${currentResult.totalStores} 个门店`}
                type="success"
                icon={<CheckCircleOutlined />}
                showIcon
              />
              
              <Table
                columns={resultColumns}
                dataSource={currentResult.details}
                rowKey="sheetName"
                pagination={false}
                size="small"
                summary={(pageData) => (
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={2}>
                      <strong>总计</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={2}>
                      <strong>{pageData.reduce((sum, item) => sum + item.accountsCreated, 0)}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3}>
                      <strong>{pageData.reduce((sum, item) => sum + item.accountsReused, 0)}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={4}>
                      <strong>{pageData.reduce((sum, item) => sum + item.storesCreated, 0)}</strong>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={5}>
                      <strong>{pageData.reduce((sum, item) => sum + item.storesSkipped, 0)}</strong>
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                )}
              />
            </Space>
          </div>
        )}
      </Modal>

      <Modal
        title="错误详情"
        open={errorModalVisible}
        onCancel={() => setErrorModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setErrorModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {currentError && (
          <Alert
            message="导入失败"
            description={
              <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                {currentError}
              </div>
            }
            type="error"
            showIcon
          />
        )}
      </Modal>
    </div>
  );
};

export default ImportData;