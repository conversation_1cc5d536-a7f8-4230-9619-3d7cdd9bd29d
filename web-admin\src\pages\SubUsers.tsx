import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Card,
  Table,
  Button,
  Tag,
  Space,
  Typography,
  Spin,
  Alert,
  Tooltip,
} from 'antd';
import {
  EyeOutlined,
  UserOutlined,
  ShopOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { webService, type SubUser } from '../services/web';
import dayjs from 'dayjs';

const { Title } = Typography;

const SubUsers: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [subUsers, setSubUsers] = useState<SubUser[]>([]);
  const navigate = useNavigate();

  useEffect(() => {
    fetchSubUsers();
  }, []);

  const fetchSubUsers = async () => {
    try {
      setLoading(true);
      const data = await webService.getSubUsers();
      setSubUsers(data);
    } catch (error) {
      console.error('获取子账号列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewDetail = (record: SubUser) => {
    navigate(`/web/sub-users/${record.id}`);
  };

  const columns: ColumnsType<SubUser> = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text: string) => (
        <Space>
          <UserOutlined />
          <strong>{text}</strong>
        </Space>
      ),
    },
    {
      title: '联系方式',
      key: 'contact',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          {record.phone && <div>📱 {record.phone}</div>}
          {record.email && <div>📧 {record.email}</div>}
        </Space>
      ),
    },
    {
      title: '公司',
      dataIndex: 'company',
      key: 'company',
      render: (text: string) => text || '-',
    },
    {
      title: '抖店数据',
      key: 'douyin',
      render: (_, record) => (
        <Space>
          <Tooltip title="抖店账号数量">
            <Tag color="blue" icon={<DatabaseOutlined />}>
              {record.stats.douyinAccountCount}
            </Tag>
          </Tooltip>
          <Tooltip title="抖店门店数量">
            <Tag color="green" icon={<ShopOutlined />}>
              {record.stats.douyinStoreCount}
            </Tag>
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '清凉数据',
      key: 'qingliang',
      render: (_, record) => (
        <Space>
          <Tooltip title="清凉账号数量">
            <Tag color="purple" icon={<DatabaseOutlined />}>
              {record.stats.qingliangAccountCount}
            </Tag>
          </Tooltip>
          <Tooltip title="清凉门店数量">
            <Tag color="orange" icon={<ShopOutlined />}>
              {record.stats.qingliangStoreCount}
            </Tag>
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button
          type="primary"
          icon={<EyeOutlined />}
          size="small"
          onClick={() => handleViewDetail(record)}
        >
          查看详情
        </Button>
      ),
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <Title level={2}>子账号管理</Title>
      
      <Card>
        {subUsers.length === 0 ? (
          <Alert
            message="暂无子账号"
            description="当前主账号下还没有子账号"
            type="info"
            showIcon
          />
        ) : (
          <Table
            columns={columns}
            dataSource={subUsers}
            rowKey="id"
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => 
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
            summary={(pageData) => (
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={3}>
                  <strong>总计</strong>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={3}>
                  <Space>
                    <Tag color="blue">
                      账号: {pageData.reduce((sum, item) => sum + item.stats.douyinAccountCount, 0)}
                    </Tag>
                    <Tag color="green">
                      门店: {pageData.reduce((sum, item) => sum + item.stats.douyinStoreCount, 0)}
                    </Tag>
                  </Space>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={4}>
                  <Space>
                    <Tag color="purple">
                      账号: {pageData.reduce((sum, item) => sum + item.stats.qingliangAccountCount, 0)}
                    </Tag>
                    <Tag color="orange">
                      门店: {pageData.reduce((sum, item) => sum + item.stats.qingliangStoreCount, 0)}
                    </Tag>
                  </Space>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5} colSpan={2} />
              </Table.Summary.Row>
            )}
          />
        )}
      </Card>
    </div>
  );
};

export default SubUsers;