import { Controller, Get, Post, Body, UseGuards, Request } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AccountService } from './account.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CreateAccountDto } from './dto/create-account.dto';

@ApiTags('账号')
@ApiBearerAuth('access-token')
@Controller('accounts')
export class AccountController {
  constructor(private readonly accountService: AccountService) {}

  @ApiOperation({ summary: '创建账号', description: '为当前登录用户创建新账号' })
  @ApiResponse({ status: 201, description: '账号创建成功' })
  @ApiResponse({ status: 401, description: '未授权' })
  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Body() createAccountDto: CreateAccountDto, @Request() req) {
    return this.accountService.create({
      ...createAccountDto,
      userId: req.user.id,
    });
  }

  @ApiOperation({ summary: '获取账号列表', description: '获取当前登录用户的所有账号' })
  @ApiResponse({ status: 200, description: '成功获取账号列表' })
  @ApiResponse({ status: 401, description: '未授权' })
  @UseGuards(JwtAuthGuard)
  @Get()
  findAll(@Request() req) {
    return this.accountService.findByUserId(req.user.id);
  }

  @ApiOperation({
    summary: '获取账号和门店列表',
    description: '获取当前登录用户的所有账号及其关联的门店'
  })
  @ApiResponse({ status: 200, description: '成功获取账号和门店列表' })
  @ApiResponse({ status: 401, description: '未授权' })
  @UseGuards(JwtAuthGuard)
  @Get('with-stores')
  getUserAccountsWithStores(@Request() req) {
    return this.accountService.getUserAccountsWithStores(req.user.id);
  }
}
