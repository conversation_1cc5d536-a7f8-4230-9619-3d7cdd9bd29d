# Ubuntu Nginx 配置说明

## 1. 安装 Nginx

```bash
# 更新系统包
sudo apt update
sudo apt upgrade -y

# 安装 Nginx
sudo apt install nginx -y

# 检查 Nginx 状态
sudo systemctl status nginx
```

## 2. 基础配置

### 2.1 目录结构说明

```plaintext
/etc/nginx/                # Nginx 配置目录
├── nginx.conf            # 主配置文件
├── sites-available/      # 网站配置文件存放目录
│   └── default          # 默认配置文件
└── sites-enabled/       # 已启用的网站配置（符号链接）
    └── default -> ../sites-available/default
```

### 2.2 创建网站配置

```bash
# 创建新的配置文件
sudo nano /etc/nginx/sites-available/my-api

# 配置文件内容示例（用于 Node.js API 服务）
server {
    listen 80;
    server_name api.example.com;  # 替换为你的域名

    # SSL 配置（如果需要）
    # listen 443 ssl;
    # ssl_certificate /path/to/cert.pem;
    # ssl_certificate_key /path/to/key.pem;

    # 日志配置
    access_log /var/log/nginx/api-access.log;
    error_log /var/log/nginx/api-error.log;

    # API 反向代理
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

### 2.3 启用配置

```bash
# 创建符号链接
sudo ln -s /etc/nginx/sites-available/my-api /etc/nginx/sites-enabled/

# 测试配置是否正确
sudo nginx -t

# 如果测试通过，重启 Nginx
sudo systemctl restart nginx
```

## 3. SSL 配置（HTTPS）

### 3.1 安装 Certbot

```bash
# 安装 Certbot
sudo apt install certbot python3-certbot-nginx -y

# 获取并安装证书
sudo certbot --nginx -d api.example.com

# 测试自动续期
sudo certbot renew --dry-run
```

### 3.2 强制 HTTPS 跳转

```nginx
server {
    listen 80;
    server_name api.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name api.example.com;
    
    # SSL 配置
    ssl_certificate /etc/letsencrypt/live/api.example.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.example.com/privkey.pem;
    
    # ... 其他配置 ...
}
```

## 4. 安全配置

### 4.1 基础安全设置

```nginx
# 在 http 块中添加
http {
    # 隐藏版本号
    server_tokens off;
    
    # 配置安全头信息
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Content-Type-Options "nosniff";
    
    # 配置 SSL 参数
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
}
```

### 4.2 配置防火墙

```bash
# 允许 HTTP
sudo ufw allow 80/tcp

# 允许 HTTPS
sudo ufw allow 443/tcp

# 允许 SSH（如果需要）
sudo ufw allow 22/tcp

# 启用防火墙
sudo ufw enable
```

## 5. 性能优化

### 5.1 启用 Gzip 压缩

```nginx
# 在 http 块中添加
gzip on;
gzip_vary on;
gzip_proxied any;
gzip_comp_level 6;
gzip_types text/plain text/css text/xml application/json application/javascript application/xml+rss application/atom+xml image/svg+xml;
```

### 5.2 配置缓存

```nginx
# 在 server 块中添加
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 30d;
    add_header Cache-Control "public, no-transform";
}
```

## 6. 日志管理

### 6.1 配置日志轮转

```bash
# 编辑 Nginx 日志轮转配置
sudo nano /etc/logrotate.d/nginx

# 添加以下内容
/var/log/nginx/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 www-data adm
    sharedscripts
    prerotate
        if [ -d /etc/logrotate.d/httpd-prerotate ]; then \
            run-parts /etc/logrotate.d/httpd-prerotate; \
        fi \
    endscript
    postrotate
        invoke-rc.d nginx rotate >/dev/null 2>&1
    endscript
}
```

## 7. 常用命令

```bash
# 启动 Nginx
sudo systemctl start nginx

# 停止 Nginx
sudo systemctl stop nginx

# 重启 Nginx
sudo systemctl restart nginx

# 重新加载配置
sudo systemctl reload nginx

# 检查配置语法
sudo nginx -t

# 查看错误日志
sudo tail -f /var/log/nginx/error.log

# 查看访问日志
sudo tail -f /var/log/nginx/access.log
```

## 8. 故障排查

### 8.1 常见问题解决

1. 502 Bad Gateway
```bash
# 检查后端服务是否运行
pm2 status

# 检查 Nginx 错误日志
sudo tail -f /var/log/nginx/error.log
```

2. 权限问题
```bash
# 设置正确的目录权限
sudo chown -R www-data:www-data /var/www/
sudo chmod -R 755 /var/www/
```

3. SSL 证书问题
```bash
# 检查证书状态
sudo certbot certificates

# 续期证书
sudo certbot renew
```

## 9. 维护建议

1. 定期检查日志文件大小
2. 监控服务器资源使用情况
3. 定期更新 Nginx 版本
4. 备份重要配置文件
5. 定期检查 SSL 证书有效期

## 10. 参考链接

- [Nginx 官方文档](https://nginx.org/en/docs/)
- [Certbot 文档](https://certbot.eff.org/docs)
- [Mozilla SSL 配置生成器](https://ssl-config.mozilla.org/)