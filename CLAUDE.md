# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a full-stack management system built with NestJS (backend) and React (frontend) for managing user accounts, stores, and quotas across DOUYIN and QINGLIANG platforms. The system features a hierarchical user structure with role-based access control.

## Development Commands

### Backend Development
```bash
# Start development server (port 3000)
npm run start:dev

# Build for production
npm run build

# Start production server
npm run start:prod

# Code quality
npm run lint
npm run format

# Testing
npm run test
npm run test:e2e
npm run test:cov
```

### Database Operations
```bash
# Generate Prisma client
npm run prisma:generate

# Run database migrations
npm run prisma:migrate

# Open Prisma Studio
npm run prisma:studio

# Seed database
npm run db:seed
```

### Frontend Development
```bash
cd web-admin
npm run dev        # Development server
npm run build      # Production build
```

### Quick Fix Commands (from README)
```bash
# Clear caches
rm -rf dist
rm -rf node_modules/.cache

# Regenerate Prisma
npx prisma generate

# Restart development
npm run start:dev
```

## Architecture Overview

### Backend Structure
- **Modular NestJS Architecture**: Each feature is organized into modules (auth, user, account, store, quota, import, etc.)
- **Database**: SQLite with Prisma ORM
- **Authentication**: JWT-based with role hierarchy (SUPER_ADMIN, MAIN, CUSTOMER)
- **API Documentation**: Swagger at `/api-docs`

### Key Backend Modules
- `auth/` - JWT authentication and authorization
- `user/` - Hierarchical user management with parent-child relationships
- `account/` - DOUYIN/QINGLIANG account management
- `store/` - Store management linked to accounts
- `quota/` - Resource quota tracking and enforcement
- `import/` - Excel file import/export functionality
- `web/` - Web-specific endpoints for the admin panel

### Frontend Structure
- **React 19 with TypeScript**
- **Ant Design UI components**
- **Vite build system**
- **React Router v7**
- Located in `web-admin/` directory

### Database Schema
- **User**: Main entity with role hierarchy and quota fields
- **Account**: Platform accounts (DOUYIN/QINGLIANG) linked to users
- **Store**: Individual stores linked to accounts
- **KeyvalueInfo**: Configuration and metadata storage

Key relationships:
- User 1:N Account (hierarchical parent-child user relationships)
- Account 1:N Store
- Users can have multiple accounts across different platforms

## Development Workflow

1. **Start Backend**: `npm run start:dev` (runs on port 3000)
2. **Start Frontend**: `cd web-admin && npm run dev`
3. **Access Swagger**: `http://localhost:3000/api-docs`
4. **Access Web Admin**: `http://localhost:3000/web`

## Important Notes

- The backend serves the frontend build at `/web` route via NestJS static files
- Environment configuration: `.env` contains production database directory
- Local development: Check `web-admin/src/services/api.ts` for API endpoint configuration
- The system supports Excel import/export for bulk operations
- Password hashing uses MD5 (consider bcrypt for production)
- Frontend proxy configuration in `web-admin/vite.config.ts` for development

## File Structure Notes

- `src/` - Backend NestJS application
- `web-admin/` - Frontend React application
- `prisma/` - Database schema and migrations
- `test/` - Backend tests
- `/public/web` - Frontend build output (served by backend)

## Testing

- Unit tests: `npm run test`
- E2E tests: `npm run test:e2e`
- Coverage: `npm run test:cov`
- Jest configuration in package.json