import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  InputNumber,
  Button,
  Space,
  Typography,
  Alert,
  Row,
  Col,
  Divider,
  Spin,
  message,
  Select,
  Table,
  Modal,
  Radio,
  Popconfirm,
  Tag,
} from 'antd';
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  UserOutlined,
  DeleteOutlined,
  ClearOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { webService, type QuotaLimits, type DashboardData, type MainAccount, type SubUserWithStats } from '../services/web';
import { authService } from '../services/auth';

const { Title, Text } = Typography;
const { Option } = Select;

const QuotaSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [mainAccounts, setMainAccounts] = useState<MainAccount[]>([]);
  const [selectedMainAccountId, setSelectedMainAccountId] = useState<string>('');
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  
  // 数据清空相关状态
  const [subUsers, setSubUsers] = useState<SubUserWithStats[]>([]);
  const [subUsersLoading, setSubUsersLoading] = useState(false);
  const [clearModalVisible, setClearModalVisible] = useState(false);
  const [clearLoading, setClearLoading] = useState(false);
  const [selectedSubUser, setSelectedSubUser] = useState<SubUserWithStats | null>(null);
  const [clearOptions, setClearOptions] = useState({
    accountType: 'ALL' as 'DOUYIN' | 'QINGLIANG' | 'ALL',
    dataType: 'all' as 'accounts' | 'stores' | 'all',
  });

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      setLoading(true);

      // 获取当前用户信息
      const user = authService.getCurrentUser();
      setCurrentUser(user);

      if (user?.role === 'SUPER_ADMIN') {
        setIsSuperAdmin(true);
        
        try {
          // 超级管理员：获取所有主账号列表
          const accounts = await webService.getMainAccounts();
          const accountsList = Array.isArray(accounts) ? accounts : [];
          setMainAccounts(accountsList);

          if (accountsList.length > 0) {
            // 默认选择第一个主账号
            const firstAccountId = accountsList[0].id;
            setSelectedMainAccountId(firstAccountId);
            
            // 分别处理这两个请求，避免一个失败影响另一个
            try {
              await fetchMainAccountData(firstAccountId);
            } catch (error) {
              console.error('获取主账号数据失败:', error);
            }
            
            try {
              await fetchSubUsers(firstAccountId);
            } catch (error) {
              console.error('获取子账号列表失败:', error);
            }
          }
        } catch (error) {
          console.error('获取主账号列表失败:', error);
          message.error('获取主账号列表失败');
          setMainAccounts([]);
        }
      } else if (user?.role === 'MAIN') {
        // 主账号：获取自己的配额数据
        const data = await webService.getDashboard();
        setDashboardData(data);
        form.setFieldsValue(data.quotaLimits);
      } else {
        // 普通用户无权访问
        message.error('您没有权限访问此页面');
        return;
      }
    } catch (error: any) {
      console.error('初始化数据失败:', error);
      if (error.response?.status === 400) {
        message.error('您没有权限访问此页面');
      } else {
        message.error('获取数据失败');
      }
    } finally {
      setLoading(false);
    }
  };

  const fetchMainAccountData = async (mainAccountId: string) => {
    try {
      const data = await webService.getMainAccountQuota(mainAccountId);
      setDashboardData(data);
      form.setFieldsValue(data.quotaLimits);
    } catch (error) {
      console.error('获取主账号配额数据失败:', error);
      message.error('获取主账号配额数据失败');
    }
  };

  const handleMainAccountChange = async (mainAccountId: string) => {
    setSelectedMainAccountId(mainAccountId);
    await fetchMainAccountData(mainAccountId);
    // 同时获取子账号列表
    if (isSuperAdmin && mainAccountId) {
      await fetchSubUsers(mainAccountId);
    }
  };

  const fetchSubUsers = async (mainAccountId: string) => {
    try {
      setSubUsersLoading(true);
      console.log('正在获取子账号列表，主账号ID:', mainAccountId);
      const subUsersList = await webService.getSubUsersByMainAccount(mainAccountId);
      console.log('子账号列表响应:', subUsersList);
      // 确保返回的是数组
      const safeSubUsers = Array.isArray(subUsersList) ? subUsersList : [];
      console.log('设置子账号列表:', safeSubUsers);
      setSubUsers(safeSubUsers);
    } catch (error) {
      console.error('获取子账号列表失败:', error);
      message.error('获取子账号列表失败');
      setSubUsers([]); // 出错时设置为空数组
    } finally {
      setSubUsersLoading(false);
    }
  };

  const handleSubmit = async (values: QuotaLimits) => {
    try {
      setSaving(true);

      const submitData = isSuperAdmin
        ? { ...values, mainAccountId: selectedMainAccountId }
        : values;

      await webService.updateQuotaSettings(submitData);
      message.success('配额设置更新成功');

      // 重新获取数据
      if (isSuperAdmin && selectedMainAccountId) {
        await fetchMainAccountData(selectedMainAccountId);
      } else {
        await initializeData();
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '配额设置更新失败';
      message.error(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  const handleReset = () => {
    if (dashboardData) {
      form.setFieldsValue(dashboardData.quotaLimits);
    }
  };

  const handleClearData = (subUser: SubUserWithStats) => {
    setSelectedSubUser(subUser);
    setClearModalVisible(true);
    setClearOptions({
      accountType: 'ALL',
      dataType: 'all',
    });
  };

  const confirmClearData = async () => {
    if (!selectedSubUser) return;

    try {
      setClearLoading(true);
      const result = await webService.clearSubUserData(selectedSubUser.id, clearOptions);
      message.success(result.message);
      setClearModalVisible(false);
      
      // 重新获取数据
      if (selectedMainAccountId) {
        await Promise.all([
          fetchMainAccountData(selectedMainAccountId),
          fetchSubUsers(selectedMainAccountId),
        ]);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '数据清空失败';
      message.error(errorMessage);
    } finally {
      setClearLoading(false);
    }
  };

  // 权限检查
  if (!currentUser || (currentUser.role !== 'SUPER_ADMIN' && currentUser.role !== 'MAIN')) {
    return (
      <Alert
        message="权限不足"
        description="您没有权限访问此页面。只有超级管理员和主账号可以设置配额。"
        type="error"
        showIcon
      />
    );
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <Alert
        message="数据加载失败"
        description="无法加载配额数据，请刷新页面重试"
        type="error"
        showIcon
        action={
          <Button onClick={initializeData} icon={<ReloadOutlined />}>
            重新加载
          </Button>
        }
      />
    );
  }

  const { quotaStats, quotaLimits, isWithinLimit, exceededTypes } = dashboardData;

  const formatQuotaDisplay = (used: number, total: number) => {
    return total === 0 ? `${used} / 无限制` : `${used} / ${total}`;
  };

  const subUserColumns = [
    {
      title: '子账号',
      dataIndex: 'username',
      key: 'username',
      render: (username: string, record: SubUserWithStats) => {
        if (!record) return username;
        return (
          <Space>
            <span>{username}</span>
            <Tag color="blue">{record.role}</Tag>
          </Space>
        );
      },
    },
    {
      title: '抖店账号',
      dataIndex: ['stats', 'douyinAccountCount'],
      key: 'douyinAccountCount',
      align: 'center' as const,
      render: (value: number) => value || 0,
    },
    {
      title: '清凉账号',
      dataIndex: ['stats', 'qingliangAccountCount'],
      key: 'qingliangAccountCount',
      align: 'center' as const,
      render: (value: number) => value || 0,
    },
    {
      title: '抖店门店',
      dataIndex: ['stats', 'douyinStoreCount'],
      key: 'douyinStoreCount',
      align: 'center' as const,
      render: (value: number) => value || 0,
    },
    {
      title: '清凉门店',
      dataIndex: ['stats', 'qingliangStoreCount'],
      key: 'qingliangStoreCount',
      align: 'center' as const,
      render: (value: number) => value || 0,
    },
    {
      title: '总数据量',
      key: 'totalData',
      align: 'center' as const,
      render: (_: any, record: SubUserWithStats) => {
        if (!record || !record.stats) return '-';
        return (
          <Space direction="vertical" size="small">
            <Text type="secondary">账号: {record.stats.totalAccountCount || 0}</Text>
            <Text type="secondary">门店: {record.stats.totalStoreCount || 0}</Text>
          </Space>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      align: 'center' as const,
      render: (_: any, record: SubUserWithStats) => {
        if (!record) return null;
        return (
          <Popconfirm
            title="确认清空数据"
            description="此操作不可恢复，请确认是否继续？"
            onConfirm={() => handleClearData(record)}
            okText="确认"
            cancelText="取消"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              清空数据
            </Button>
          </Popconfirm>
        );
      },
    },
  ];

  return (
    <div>
      <Title level={2}>
        配额设置
        {isSuperAdmin && (
          <Text type="secondary" style={{ fontSize: '14px', fontWeight: 'normal', marginLeft: '16px' }}>
            超级管理员模式
          </Text>
        )}
      </Title>

      {isSuperAdmin && (
        <Card title="选择主账号" style={{ marginBottom: 24 }} extra={<UserOutlined />}>
          <Form.Item
            label="主账号"
            style={{ marginBottom: 0 }}
          >
            <Select
              value={selectedMainAccountId}
              onChange={handleMainAccountChange}
              placeholder="请选择要设置配额的主账号"
              style={{ width: '100%' }}
              showSearch
              optionFilterProp="children"
            >
              {(mainAccounts || []).map(account => (
                <Option key={account.id} value={account.id}>
                  <Space>
                    <span>{account.username}</span>
                    {account.company && (
                      <Text type="secondary">({account.company})</Text>
                    )}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Card>
      )}

      {!isWithinLimit && (
        <Alert
          message="当前配额超限"
          description={`以下类型已超出配额限制：${exceededTypes.join('、')}。请适当增加配额限制。`}
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="当前用量统计" extra={<SettingOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <div>
                <Text strong>抖店账号：</Text>
                <Text>{formatQuotaDisplay(quotaStats.douyinAccountCount, quotaLimits.totalDouyinAccountQuota)}</Text>
              </div>
              <div>
                <Text strong>清凉账号：</Text>
                <Text>{formatQuotaDisplay(quotaStats.qingliangAccountCount, quotaLimits.totalQingliangAccountQuota)}</Text>
              </div>
              <div>
                <Text strong>抖店门店：</Text>
                <Text>{formatQuotaDisplay(quotaStats.douyinStoreCount, quotaLimits.totalDouyinStoreQuota)}</Text>
              </div>
              <div>
                <Text strong>清凉门店：</Text>
                <Text>{formatQuotaDisplay(quotaStats.qingliangStoreCount, quotaLimits.totalQingliangStoreQuota)}</Text>
              </div>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title="配额设置" extra={<SettingOutlined />}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              autoComplete="off"
            >
              <Divider orientation="left">抖店配额</Divider>
              
              <Form.Item
                label="抖店账号数量配额"
                name="totalDouyinAccountQuota"
                rules={[
                  { required: true, message: '请输入抖店账号数量配额' },
                  { type: 'number', min: 0, message: '配额不能小于0' },
                ]}
                extra={`当前已使用：${quotaStats.douyinAccountCount}`}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入抖店账号数量配额"
                  min={0}
                />
              </Form.Item>

              <Form.Item
                label="抖店门店数量配额"
                name="totalDouyinStoreQuota"
                rules={[
                  { required: true, message: '请输入抖店门店数量配额' },
                  { type: 'number', min: 0, message: '配额不能小于0' },
                ]}
                extra={`当前已使用：${quotaStats.douyinStoreCount}`}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入抖店门店数量配额"
                  min={0}
                />
              </Form.Item>

              <Divider orientation="left">清凉配额</Divider>

              <Form.Item
                label="清凉账号数量配额"
                name="totalQingliangAccountQuota"
                rules={[
                  { required: true, message: '请输入清凉账号数量配额' },
                  { type: 'number', min: 0, message: '配额不能小于0' },
                ]}
                extra={`当前已使用：${quotaStats.qingliangAccountCount}`}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入清凉账号数量配额"
                  min={0}
                />
              </Form.Item>

              <Form.Item
                label="清凉门店数量配额"
                name="totalQingliangStoreQuota"
                rules={[
                  { required: true, message: '请输入清凉门店数量配额' },
                  { type: 'number', min: 0, message: '配额不能小于0' },
                ]}
                extra={`当前已使用：${quotaStats.qingliangStoreCount}`}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入清凉门店数量配额"
                  min={0}
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={saving}
                    icon={<SaveOutlined />}
                  >
                    保存设置
                  </Button>
                  <Button
                    onClick={handleReset}
                    icon={<ReloadOutlined />}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </Col>
      </Row>

      {/* 子账号数据管理 - 仅超级管理员可见 */}
      {isSuperAdmin && selectedMainAccountId && (
        <Card 
          title="子账号数据管理" 
          style={{ marginTop: 24 }}
          extra={<ClearOutlined />}
        >
          <Alert
            message="数据清空功能"
            description="超级管理员可以清空指定子账号的数据，此操作不可恢复，请谨慎使用。"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />
          
          <Table
            columns={subUserColumns}
            dataSource={subUsers || []}
            rowKey="id"
            loading={subUsersLoading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个子账号`,
            }}
            size="small"
            scroll={{ x: 800 }}
          />
        </Card>
      )}

      {/* 清空数据确认模态框 */}
      <Modal
        title="清空数据设置"
        open={clearModalVisible}
        onCancel={() => setClearModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setClearModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="confirm"
            type="primary"
            danger
            loading={clearLoading}
            onClick={confirmClearData}
          >
            确认清空
          </Button>,
        ]}
        width={500}
      >
        {selectedSubUser && (
          <div>
            <Alert
              message="警告"
              description={`即将清空子账号 "${selectedSubUser.username}" 的数据，此操作不可恢复！`}
              type="error"
              showIcon
              style={{ marginBottom: 24 }}
            />

            <Form layout="vertical">
              <Form.Item label="账号类型">
                <Radio.Group
                  value={clearOptions.accountType}
                  onChange={(e) => setClearOptions({ ...clearOptions, accountType: e.target.value })}
                >
                  <Radio value="ALL">全部类型</Radio>
                  <Radio value="DOUYIN">仅抖店</Radio>
                  <Radio value="QINGLIANG">仅清凉</Radio>
                </Radio.Group>
              </Form.Item>

              <Form.Item label="数据类型">
                <Radio.Group
                  value={clearOptions.dataType}
                  onChange={(e) => setClearOptions({ ...clearOptions, dataType: e.target.value })}
                >
                  <Radio value="all">账号和门店</Radio>
                  <Radio value="accounts">仅账号数据</Radio>
                  <Radio value="stores">仅门店数据</Radio>
                </Radio.Group>
              </Form.Item>

              <Divider />

              <div>
                <Text strong>当前数据统计：</Text>
                <div style={{ marginTop: 8 }}>
                  <Text>抖店账号：{selectedSubUser.stats.douyinAccountCount} 个</Text><br />
                  <Text>清凉账号：{selectedSubUser.stats.qingliangAccountCount} 个</Text><br />
                  <Text>抖店门店：{selectedSubUser.stats.douyinStoreCount} 个</Text><br />
                  <Text>清凉门店：{selectedSubUser.stats.qingliangStoreCount} 个</Text>
                </div>
              </div>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default QuotaSettings;