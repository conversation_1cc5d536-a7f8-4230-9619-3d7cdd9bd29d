import { Injectable, UnauthorizedException, ForbiddenException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../prisma/prisma.service';
import { UserRole } from '@prisma/client';
import * as crypto from 'crypto';

@Injectable()
export class AuthService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly jwtService: JwtService,
  ) {}

  // MD5加密
  private md5(text: string): string {
    return crypto.createHash('md5').update(text).digest('hex');
  }

  async validateUser(username: string, password: string) {
    const user = await this.prismaService.user.findUnique({
      where: { username },
    });

    if (!user) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 验证密码（MD5加密）
    const hashedPassword = this.md5(password);
    if (user.password !== hashedPassword) {
      throw new UnauthorizedException('用户名或密码错误');
    }

    // 不返回密码
    const { password: _, ...result } = user;
    return result;
  }

  async login(username: string, password: string) {
    const user = await this.validateUser(username, password);
    
    const payload = { sub: user.id, username: user.username };
    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }

  async webLogin(username: string, password: string) {
    const user = await this.validateUser(username, password);

    // 检查是否为主账号或超级管理员
    if (user.role !== UserRole.MAIN && user.role !== UserRole.SUPER_ADMIN) {
      throw new ForbiddenException('只有主账号和超级管理员可以登录Web管理系统');
    }
    
    const payload = { sub: user.id, username: user.username };
    return {
      access_token: this.jwtService.sign(payload),
      user,
    };
  }
}
