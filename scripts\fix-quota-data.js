const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function fixQuotaData() {
  console.log('开始修复配额字段数据...');
  
  try {
    // 修复所有用户的配额字段，将空字符串转换为NULL
    const result1 = await prisma.$executeRaw`
      UPDATE User 
      SET totalDouyinAccountQuota = NULL 
      WHERE totalDouyinAccountQuota = ''
    `;
    
    const result2 = await prisma.$executeRaw`
      UPDATE User 
      SET totalQingliangAccountQuota = NULL 
      WHERE totalQingliangAccountQuota = ''
    `;
    
    const result3 = await prisma.$executeRaw`
      UPDATE User 
      SET totalDouyinStoreQuota = NULL 
      WHERE totalDouyinStoreQuota = ''
    `;
    
    const result4 = await prisma.$executeRaw`
      UPDATE User 
      SET totalQingliangStoreQuota = NULL 
      WHERE totalQingliangStoreQuota = ''
    `;
    
    console.log('配额字段数据修复完成！');
    console.log(`- totalDouyinAccountQuota: ${result1} 条记录已修复`);
    console.log(`- totalQingliangAccountQuota: ${result2} 条记录已修复`);
    console.log(`- totalDouyinStoreQuota: ${result3} 条记录已修复`);
    console.log(`- totalQingliangStoreQuota: ${result4} 条记录已修复`);
    
    // 验证修复结果
    const usersWithEmptyQuota = await prisma.user.findMany({
      where: {
        OR: [
          { totalDouyinAccountQuota: { equals: '' } },
          { totalQingliangAccountQuota: { equals: '' } },
          { totalDouyinStoreQuota: { equals: '' } },
          { totalQingliangStoreQuota: { equals: '' } }
        ]
      },
      select: {
        id: true,
        username: true,
        totalDouyinAccountQuota: true,
        totalQingliangAccountQuota: true,
        totalDouyinStoreQuota: true,
        totalQingliangStoreQuota: true
      }
    });
    
    if (usersWithEmptyQuota.length > 0) {
      console.log('\n警告：仍有用户存在空字符串配额字段：');
      usersWithEmptyQuota.forEach(user => {
        console.log(`- 用户 ${user.username} (${user.id})`);
      });
    } else {
      console.log('\n✅ 所有配额字段数据已成功修复！');
    }
    
  } catch (error) {
    console.error('修复配额字段数据时出错：', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行修复脚本
fixQuotaData();
