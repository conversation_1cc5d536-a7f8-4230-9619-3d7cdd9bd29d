# Web管理系统

基于 React + TypeScript + Antd 的现代化账号管理系统，为后端API系统提供完整的Web管理界面。

## 🚀 功能特性

- 🔐 **主账号专用登录**：只有MAIN角色的主账号可以访问
- 📊 **实时仪表板**：配额使用情况、数据统计、超限预警
- 👥 **子账号管理**：查看和管理所有子账号及其绑定数据
- ⚙️ **配额设置**：灵活的配额管理和限制设置
- 📂 **智能导入**：支持选择子账号的Excel数据导入功能
- 🎨 **响应式设计**：支持桌面端和移动端访问

## 🛠️ 技术栈

- **框架**：React 18 + TypeScript
- **UI库**：Ant Design 5.x
- **构建工具**：Vite 6.x
- **路由**：React Router Dom 6.x
- **HTTP客户端**：Axios
- **日期处理**：Day.js
- **样式方案**：CSS-in-JS（Ant Design）

## 📋 开发环境要求

- Node.js >= 18.x
- NPM >= 9.x 或 Yarn >= 1.22.x
- 现代浏览器（Chrome、Firefox、Safari、Edge）

## 🔧 开发指南

### 环境安装

```bash
# 进入前端项目目录
cd web-admin

# 安装依赖
npm install

# 或使用 yarn
yarn install
```

### 开发服务器

```bash
# 启动开发服务器
npm run dev

# 或使用 yarn
yarn dev
```

开发服务器将在 `http://localhost:5173` 启动，支持热重载。

### 开发配置

开发环境下，API请求会通过Vite代理转发到后端服务：

```typescript
// vite.config.ts
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:3000',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    }
  }
}
```

### 项目结构

```
web-admin/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 通用组件
│   │   ├── Layout.tsx     # 主布局组件
│   │   └── ProtectedRoute.tsx # 路由保护组件
│   ├── pages/             # 页面组件
│   │   ├── Login.tsx      # 登录页面
│   │   ├── Dashboard.tsx  # 仪表板
│   │   ├── SubUsers.tsx   # 子账号列表
│   │   ├── SubUserDetail.tsx # 子账号详情
│   │   ├── QuotaSettings.tsx # 配额设置
│   │   └── ImportData.tsx # 数据导入
│   ├── services/          # API服务
│   │   ├── api.ts         # Axios配置
│   │   ├── auth.ts        # 认证服务
│   │   └── web.ts         # Web管理API
│   ├── router/            # 路由配置
│   │   └── index.tsx      # 路由定义
│   ├── App.tsx            # 根组件
│   └── main.tsx           # 应用入口
├── package.json           # 项目配置
├── vite.config.ts         # Vite配置
└── tsconfig.json          # TypeScript配置
```

### 代码规范

项目使用以下代码规范：

```bash
# TypeScript严格模式
"strict": true,
"verbatimModuleSyntax": true

# ESLint配置
npm run lint

# 格式化代码（如果配置了）
npm run format
```

### API集成

所有API调用都通过service层进行：

```typescript
// 示例：获取仪表板数据
import { webService } from '../services/web';

const Dashboard = () => {
  useEffect(() => {
    const fetchData = async () => {
      try {
        const data = await webService.getDashboard();
        setDashboardData(data);
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    };
    fetchData();
  }, []);
};
```

## 🏗️ 构建部署

### 生产构建

```bash
# 构建生产版本
npm run build

# 或使用 yarn
yarn build
```

构建产物将输出到 `../public/web/` 目录，可直接被后端静态文件服务托管。

### 构建配置

```typescript
// vite.config.ts
export default defineConfig({
  base: '/web/',                    # 部署基础路径
  build: {
    outDir: '../public/web',        # 输出目录
    emptyOutDir: true,              # 清空输出目录
  },
});
```

### 部署验证

构建完成后，可以通过以下方式验证：

```bash
# 检查构建产物
ls -la ../public/web/

# 启动后端服务器
cd .. && npm start

# 访问管理系统
# http://localhost:3000/web/
```

## 🔍 调试指南

### 开发工具

推荐使用以下浏览器扩展：

- **React Developer Tools**：React组件调试
- **Redux DevTools**：状态管理调试（如果使用）
- **Antd Design DevTools**：Ant Design组件调试

### 常见问题

#### 1. API请求失败

```bash
# 检查后端服务是否启动
curl http://localhost:3000/

# 检查代理配置
# 确保vite.config.ts中的proxy配置正确
```

#### 2. 路由404问题

```bash
# 确保后端静态文件服务配置正确
# 检查 ServeStaticModule 配置
```

#### 3. 类型错误

```bash
# 重新生成类型
npm run type-check

# 清除缓存
rm -rf node_modules/.vite
npm run dev
```

### 日志调试

```typescript
// 开发环境调试
if (import.meta.env.DEV) {
  console.log('Debug info:', data);
}

// 生产环境错误追踪
try {
  await apiCall();
} catch (error) {
  console.error('API Error:', error);
  message.error('操作失败，请重试');
}
```

## 📚 开发最佳实践

### 组件开发

```typescript
// 使用函数组件 + Hooks
const MyComponent: React.FC<Props> = ({ prop1, prop2 }) => {
  const [state, setState] = useState(initialValue);
  
  // 使用 useEffect 处理副作用
  useEffect(() => {
    // 副作用逻辑
  }, [dependencies]);
  
  return (
    <div>
      {/* JSX */}
    </div>
  );
};
```

### 错误处理

```typescript
// 统一错误处理
const handleAsync = async () => {
  try {
    const result = await apiCall();
    message.success('操作成功');
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || '操作失败';
    message.error(errorMessage);
  }
};
```

### 性能优化

```typescript
// 使用 React.memo 优化重渲染
const ExpensiveComponent = React.memo(({ data }) => {
  return <div>{/* 复杂组件 */}</div>;
});

// 使用 useMemo 缓存计算结果
const expensiveValue = useMemo(() => {
  return heavyComputation(data);
}, [data]);
```

## 🤝 贡献指南

1. **分支管理**：使用 feature/xxx 分支开发新功能
2. **提交规范**：使用语义化提交信息
3. **代码审查**：提交PR前确保代码质量
4. **测试覆盖**：为新功能添加适当的测试

## 📄 许可证

本项目采用 MIT 许可证。