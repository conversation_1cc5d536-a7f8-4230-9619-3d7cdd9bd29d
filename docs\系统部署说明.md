# 系统部署说明

包含后端API系统和Web管理系统的完整部署指南。

## 📋 系统概览

本系统包含两个主要部分：
- **后端API系统**：基于NestJS的RESTful API服务
- **Web管理系统**：基于React+Antd的前端管理界面

部署完成后，可通过以下方式访问：
- API接口：`http://your-domain.com/`
- Swagger文档：`http://your-domain.com/api-docs`
- Web管理系统：`http://your-domain.com/web/`

## 🛠️ 系统要求

### 硬件要求
- **CPU**：2核心以上
- **内存**：4GB以上（推荐8GB）
- **存储空间**：20GB以上
- **网络**：稳定的互联网连接

### 软件要求
- **操作系统**：Ubuntu Server 20.04 LTS 或更高版本
- **Node.js**：18.x 或更高版本
- **NPM**：9.x 或更高版本
- **SQLite**：3.x
- **Nginx**：1.18 或更高版本（可选，用于反向代理）
- **PM2**：最新版本（用于进程管理）

## 🚀 快速部署（生产环境）

### 1. 环境准备

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y

# 安装 Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# 安装 SQLite
sudo apt install -y sqlite3

# 安装 PM2
sudo npm install -g pm2

# 验证安装
node --version
npm --version
sqlite3 --version
pm2 --version
```

### 2. 应用部署

```bash
# 创建应用目录
sudo mkdir -p /var/www/cy-api
sudo chown -R $USER:$USER /var/www/cy-api

# 上传项目文件到 /var/www/cy-api
# 可以使用 git clone、scp、rsync 等方式

# 进入项目目录
cd /var/www/cy-api

# 安装后端依赖
npm install

# 进入前端目录安装依赖
cd web-admin
npm install

# 返回根目录
cd ..
```

### 3. 环境配置

```bash
# 创建生产环境配置
cat > .env << EOF
# 数据库配置
DATABASE_URL="file:/var/www/cy-api/prisma/prod.db"

# JWT配置
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="180d"

# 服务端口
PORT=3000

# 运行环境
NODE_ENV=production
EOF

# 设置文件权限
chmod 600 .env
```

### 4. 数据库初始化

```bash
# 生成 Prisma Client
npx prisma generate

# 执行数据库迁移
npx prisma migrate deploy

# 初始化种子数据（可选）
npm run db:seed
```

### 5. 构建应用

```bash
# 构建前端应用
cd web-admin
npm run build

# 返回根目录并构建后端
cd ..
npm run build
```

### 6. 启动服务

```bash
# 启动应用
pm2 start ecosystem.config.js

# 配置开机自启
pm2 startup
pm2 save
```

## 📁 详细部署步骤

### 后端API系统部署

#### 1. 项目结构检查

确保项目包含以下关键文件：
```
cy-api/
├── src/                    # 源代码
├── prisma/                 # 数据库配置
│   ├── schema.prisma      # 数据库模式
│   └── migrations/        # 迁移文件
├── web-admin/             # 前端源码
├── public/                # 静态文件（构建后）
│   └── web/              # 前端构建产物
├── package.json           # 后端依赖
├── ecosystem.config.js    # PM2配置
├── .env                   # 环境变量
└── docs/                  # 文档
```

#### 2. PM2 配置

创建 `ecosystem.config.js` 文件：

```javascript
module.exports = {
  apps: [{
    name: 'cy-api',
    script: 'dist/src/main.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '2G',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
```

#### 3. 数据库配置

```bash
# 确保数据库目录权限正确
sudo mkdir -p /var/www/cy-api/prisma
sudo chown -R $USER:$USER /var/www/cy-api/prisma

# 如果从开发环境迁移数据
# 可以复制现有的数据库文件
# cp /path/to/dev.db /var/www/cy-api/prisma/prod.db
```

### Web管理系统部署

#### 1. 前端构建配置

确保 `web-admin/vite.config.ts` 配置正确：

```typescript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  base: '/web/',                    // 重要：设置正确的基础路径
  build: {
    outDir: '../public/web',        // 输出到后端静态目录
    emptyOutDir: true,
    sourcemap: false,               // 生产环境关闭sourcemap
    minify: 'terser',              // 使用terser压缩
  },
})
```

#### 2. 构建验证

```bash
# 进入前端目录
cd web-admin

# 清理缓存
rm -rf node_modules/.vite
rm -rf dist

# 重新安装依赖（如果需要）
npm ci

# 执行构建
npm run build

# 检查构建产物
ls -la ../public/web/
```

#### 3. 后端静态文件服务

确保后端已配置静态文件服务（已在代码中配置）：

```typescript
// src/app.module.ts
ServeStaticModule.forRoot({
  rootPath: join(__dirname, '..', 'public', 'web'),
  serveRoot: '/web',
}),
```

## 🔧 Nginx配置（推荐）

### 1. 安装Nginx

```bash
sudo apt install -y nginx
```

### 2. 配置文件

创建站点配置：

```bash
sudo nano /etc/nginx/sites-available/cy-api
```

添加以下配置：

```nginx
server {
    listen 80;
    server_name your-domain.com;  # 替换为您的域名

    # 安全设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;

    # 日志配置
    access_log /var/log/nginx/cy-api.access.log;
    error_log /var/log/nginx/cy-api.error.log;

    # Web管理系统（前端）
    location /web/ {
        proxy_pass http://localhost:3000/web/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            proxy_pass http://localhost:3000;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API接口
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 限制请求大小（用于文件上传）
        client_max_body_size 50M;
    }

    # 健康检查
    location /health {
        proxy_pass http://localhost:3000/health;
        access_log off;
    }
}
```

### 3. 启用配置

```bash
# 启用站点
sudo ln -s /etc/nginx/sites-available/cy-api /etc/nginx/sites-enabled/

# 测试配置
sudo nginx -t

# 重启Nginx
sudo systemctl restart nginx

# 设置开机自启
sudo systemctl enable nginx
```

## 🔐 SSL配置（HTTPS）

### 使用Let's Encrypt免费证书

```bash
# 安装Certbot
sudo apt install -y certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加以下行：
# 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 监控和日志

### 1. PM2监控

```bash
# 查看应用状态
pm2 status

# 查看日志
pm2 logs cy-api

# 查看实时日志
pm2 logs cy-api --lines 50 -f

# 重启应用
pm2 restart cy-api

# 查看详细信息
pm2 show cy-api
```

### 2. 日志管理

```bash
# 创建日志目录
mkdir -p /var/www/cy-api/logs

# 配置日志轮转
sudo nano /etc/logrotate.d/cy-api
```

添加日志轮转配置：

```
/var/www/cy-api/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    notifempty
    create 644 $USER $USER
    postrotate
        pm2 reload cy-api
    endscript
}
```

## 🔄 更新部署

### 快速更新流程

```bash
# 进入项目目录
cd /var/www/cy-api

# 拉取最新代码
git pull origin main  # 或者重新上传文件

# 安装新的依赖（如果有）
npm install

# 构建前端
cd web-admin
npm install  # 如果前端有新依赖
npm run build

# 返回根目录构建后端
cd ..
npm run build

# 执行数据库迁移（如果有）
npx prisma migrate deploy

# 重启应用
pm2 restart cy-api

# 检查状态
pm2 status
pm2 logs cy-api --lines 20
```

### 数据库迁移注意事项

```bash
# 备份数据库
cp /var/www/cy-api/prisma/prod.db /backup/prod.db.$(date +%Y%m%d_%H%M%S)

# 执行迁移
npx prisma migrate deploy

# 如果迁移失败，可以恢复备份
# cp /backup/prod.db.20241219_143000 /var/www/cy-api/prisma/prod.db
```

## 🛡️ 安全配置

### 1. 防火墙设置

```bash
# 启用UFW防火墙
sudo ufw enable

# 允许SSH
sudo ufw allow ssh

# 允许HTTP/HTTPS
sudo ufw allow http
sudo ufw allow https

# 检查状态
sudo ufw status
```

### 2. 文件权限

```bash
# 设置正确的文件权限
sudo chown -R $USER:$USER /var/www/cy-api
sudo chmod -R 755 /var/www/cy-api
sudo chmod 600 /var/www/cy-api/.env
```

### 3. 环境变量安全

```bash
# 确保.env文件安全
chmod 600 .env

# 在.env中使用强密码
JWT_SECRET=$(openssl rand -base64 32)
```

## 🔍 故障排除

### 常见问题

#### 1. 应用无法启动

```bash
# 检查日志
pm2 logs cy-api

# 检查端口占用
sudo netstat -tlnp | grep :3000

# 检查环境变量
cat .env
```

#### 2. 前端页面404

```bash
# 检查构建产物
ls -la public/web/

# 检查Nginx配置
sudo nginx -t

# 检查静态文件服务
curl http://localhost:3000/web/
```

#### 3. 数据库连接问题

```bash
# 检查数据库文件权限
ls -la prisma/

# 测试数据库连接
npx prisma db pull
```

#### 4. API请求失败

```bash
# 检查后端服务
curl http://localhost:3000/

# 检查API文档
curl http://localhost:3000/api-docs
```

### 性能优化

```bash
# 启用PM2集群模式（多核服务器）
pm2 start ecosystem.config.js --env production

# 监控性能
pm2 monit
```

## 📞 技术支持

如遇到部署问题，请检查：

1. **日志文件**：`/var/www/cy-api/logs/`
2. **PM2状态**：`pm2 status`
3. **Nginx日志**：`/var/log/nginx/`
4. **系统资源**：`htop` 或 `free -h`

---

**部署完成后，系统将提供：**
- ✅ 完整的API服务
- ✅ Web管理界面
- ✅ 自动重启和监控
- ✅ 日志管理
- ✅ HTTPS安全访问（如果配置了SSL）

**访问地址：**
- Web管理系统：`https://your-domain.com/web/`
- API文档：`https://your-domain.com/api-docs`