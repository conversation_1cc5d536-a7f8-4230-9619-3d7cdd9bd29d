import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateKeyvalueDto } from './dto/create-keyvalue.dto';
import { AccessType } from '@prisma/client';

@Injectable()
export class KeyvalueService {
  constructor(private readonly prismaService: PrismaService) {}

  // 创建键值信息
  async create(createKeyvalueDto: CreateKeyvalueDto) {
    return this.prismaService.keyvalueInfo.create({
      data: createKeyvalueDto,
    });
  }

  // 通过key查询指定key信息
  async findByKey(key: string, isAuthenticated: boolean = false) {
    const keyvalue = await this.prismaService.keyvalueInfo.findUnique({
      where: { key },
    });

    if (!keyvalue) {
      throw new NotFoundException(`未找到键为 ${key} 的信息`);
    }

    // 权限检查：如果是私有的，需要登录才能访问
    if (keyvalue.accessType === AccessType.PRIVATE && !isAuthenticated) {
      throw new NotFoundException('未找到相关信息或无权访问');
    }

    return keyvalue;
  }

  // 通过prekey查询子项
  async findByPrekey(prekey: string, isAuthenticated: boolean = false) {
    const keyvalues = await this.prismaService.keyvalueInfo.findMany({
      where: { prekey },
    });

    // 权限过滤：如果未登录，只返回公开的项
    if (!isAuthenticated) {
      return keyvalues.filter(item => item.accessType === AccessType.PUBLIC);
    }

    return keyvalues;
  }

  // 通过type查询列表
  async findByType(type: string, isAuthenticated: boolean = false) {
    const keyvalues = await this.prismaService.keyvalueInfo.findMany({
      where: { type },
    });

    // 权限过滤：如果未登录，只返回公开的项
    if (!isAuthenticated) {
      return keyvalues.filter(item => item.accessType === AccessType.PUBLIC);
    }

    return keyvalues;
  }

  // 更新键值信息
  async update(id: string, updateData: Partial<CreateKeyvalueDto>) {
    return this.prismaService.keyvalueInfo.update({
      where: { id },
      data: updateData,
    });
  }

  // 删除键值信息
  async remove(id: string) {
    return this.prismaService.keyvalueInfo.delete({
      where: { id },
    });
  }
}
