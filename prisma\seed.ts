import { PrismaClient, UserRole, AccountType } from '@prisma/client';
import * as crypto from 'crypto';

const prisma = new PrismaClient();

// MD5加密
function md5(text: string): string {
  return crypto.createHash('md5').update(text).digest('hex');
}

async function main() {
  // 清空现有数据
  await prisma.store.deleteMany();
  await prisma.account.deleteMany();
  await prisma.user.deleteMany();

  console.log('数据库已清空');

  // 创建超级管理员
  const admin = await prisma.user.create({
    data: {
      username: 'admin',
      password: md5('admin123'),
      phone: '***********',
      email: '<EMAIL>',
      company: '系统管理公司',
      role: UserRole.SUPER_ADMIN,
    },
  });

  console.log('超级管理员已创建:', admin.username);

  // 创建普通用户
  const user = await prisma.user.create({
    data: {
      username: 'user',
      password: md5('user123'),
      phone: '***********',
      email: '<EMAIL>',
      company: '测试公司',
      role: UserRole.CUSTOMER,
    },
  });

  console.log('普通用户已创建:', user.username);

  // 创建主账号
  const mainUser = await prisma.user.create({
    data: {
      username: 'doudian',
      password: md5('doudian123'),
      phone: '***********',
      email: '<EMAIL>',
      company: '抖店主账号公司',
      role: UserRole.MAIN,
      totalDouyinAccountQuota: 100,
      totalQingliangAccountQuota: 100,
      totalDouyinStoreQuota: 100,
      totalQingliangStoreQuota: 100,
    },
  });

  console.log('主账号已创建:', mainUser.username);

  // 创建子账号
  const subUser = await prisma.user.create({
    data: {
      username: 'doudian01',
      password: md5('doudian01123'),
      phone: '***********',
      email: '<EMAIL>',
      company: '抖店子账号公司',
      role: UserRole.CUSTOMER,
      parentUserId: mainUser.id,
    },
  });

  console.log('子账号已创建:', subUser.username);

  // 为普通用户创建账号
  const douyinAccount = await prisma.account.create({
    data: {
      userId: user.id,
      name: '抖音账号1',
      type: AccountType.DOUYIN,
    },
  });

  const qingliangAccount = await prisma.account.create({
    data: {
      userId: user.id,
      name: '轻量账号1',
      type: AccountType.QINGLIANG,
    },
  });

  console.log('账号已创建:', douyinAccount.name, qingliangAccount.name);

  // 为账号创建门店
  const store1 = await prisma.store.create({
    data: {
      accountId: douyinAccount.id,
      name: '抖音门店1',
      shortName: '抖1',
    },
  });

  const store2 = await prisma.store.create({
    data: {
      accountId: douyinAccount.id,
      name: '抖音门店2',
      shortName: '抖2',
    },
  });

  const store3 = await prisma.store.create({
    data: {
      accountId: qingliangAccount.id,
      name: '轻量门店1',
      shortName: '轻1',
    },
  });

  console.log('门店已创建:', store1.name, store2.name, store3.name);

  console.log('数据库种子已完成');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
