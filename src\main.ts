import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Reflector } from '@nestjs/core';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { QuotaService } from './quota/quota.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 启用CORS
  app.enableCors();

  // 全局验证管道
  app.useGlobalPipes(new ValidationPipe({
    whitelist: true,
    transform: true,
  }));

  // 全局应用JWT认证守卫
  const reflector = app.get(Reflector);
  app.useGlobalGuards(new JwtAuthGuard(reflector));

  // 配置Swagger文档
  const config = new DocumentBuilder()
    .setTitle('后端API系统')
    .setDescription('')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'Authorization',
        description: '输入JWT token',
        in: 'header'
      },
      'access-token', // 这个名称用于在Swagger UI中引用
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);

  // 添加Swagger UI的高级配置
  const customOptions = {
    swaggerOptions: {
      persistAuthorization: true, // 保持授权状态
      tryItOutEnabled: true,      // 默认启用"Try it out"功能
      displayRequestDuration: true, // 显示请求持续时间
      filter: true,               // 启用过滤功能
      syntaxHighlight: {          // 语法高亮
        activate: true,
        theme: 'agate'
      },
    },
    customSiteTitle: '后端API系统文档', // 自定义网页标题
  };

  SwaggerModule.setup('api-docs', app, document, customOptions);

  // 启动时修复配额字段数据
  try {
    const quotaService = app.get(QuotaService);
    await quotaService.fixQuotaFieldsData();
    console.log('✅ 配额字段数据检查和修复完成');
  } catch (error) {
    console.warn('⚠️ 配额字段数据修复失败，但不影响应用启动:', error.message);
  }

  const port = process.env.PORT || 3000;
  await app.listen(port);
  console.log(`应用程序已启动，监听端口: ${port}`);
  console.log(`Swagger文档地址: http://localhost:${port}/api-docs`);
}
bootstrap();
