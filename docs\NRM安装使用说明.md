# NRM (Node Registry Manager) 安装和使用说明

## 1. 安装 NRM

### 1.1 全局安装
```bash
# 使用 npm 安装
npm install -g nrm

# 验证安装
nrm --version
```

## 2. 基本使用

### 2.1 查看可用镜像源
```bash
nrm ls

# 输出示例：
# npm -------- https://registry.npmjs.org/
# yarn ------- https://registry.yarnpkg.com/
# cnpm ------- https://r.cnpmjs.org/
# taobao ----- https://registry.npmmirror.com/
# npmMirror -- https://skimdb.npmjs.com/registry/
```

### 2.2 切换镜像源
```bash
# 切换到淘宝镜像
nrm use taobao

# 切换到官方镜像
nrm use npm
```

### 2.3 测试镜像速度
```bash
# 测试所有镜像源响应时间
nrm test

# 测试特定镜像源响应时间
nrm test taobao
```

### 2.4 添加自定义镜像源
```bash
# 语法：nrm add <registry-name> <registry-url>
nrm add company http://npm.company.com/
```

### 2.5 删除镜像源
```bash
# 语法：nrm del <registry-name>
nrm del company
```

## 3. 常用命令汇总

```bash
# 查看当前使用的镜像源
nrm current

# 查看所有命令
nrm help

# 查看某个命令的帮助信息
nrm help add
```

## 4. 最佳实践

### 4.1 团队开发建议
1. 统一使用相同的镜像源，避免依赖包版本不一致
2. 在项目文档中注明使用的镜像源
3. 在 CI/CD 环境中使用官方镜像源

### 4.2 镜像源选择建议
1. 开发环境：建议使用淘宝镜像（速度快）
2. 生产环境：建议使用官方镜像（稳定性好）
3. 企业内部：建议使用企业私有镜像

## 5. 故障排查

### 5.1 常见问题解决
1. 安装失败
```bash
# 清除 npm 缓存后重试
npm cache clean -f
npm install -g nrm
```

2. 切换镜像源失败
```bash
# 手动设置镜像源
npm config set registry https://registry.npmmirror.com/
```

3. 权限问题
```bash
# 使用 sudo 安装（Linux/Mac）
sudo npm install -g nrm

# 或修复 npm 权限
sudo chown -R $USER:$USER ~/.npm
```

## 6. 维护建议

1. 定期更新 NRM
```bash
# 更新到最新版本
npm update -g nrm
```

2. 定期测试镜像源速度
```bash
# 测试所有镜像源速度
nrm test
```

3. 保持镜像源列表清洁
```bash
# 删除不用的镜像源
nrm del <registry-name>
```

## 7. 参考链接

- [NRM GitHub 仓库](https://github.com/Pana/nrm)
- [NPM 官方文档](https://docs.npmjs.com/)
- [淘宝 NPM 镜像站](https://npmmirror.com/)