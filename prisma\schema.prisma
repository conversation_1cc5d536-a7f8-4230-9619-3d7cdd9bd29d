generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

enum UserRole {
  SUPER_ADMIN
  CUSTOMER
  MAIN
}

enum AccountType {
  DOUYIN
  QINGLIANG
}

enum AccessType {
  PUBLIC
  PRIVATE
}

model User {
  id                         String    @id @default(uuid())
  username                   String    @unique
  password                   String
  phone                      String?
  email                      String?
  company                    String?
  role                       UserRole  @default(CUSTOMER)
  totalDouyinAccountQuota    Int?
  totalQingliangAccountQuota Int?
  totalDouyinStoreQuota      Int?
  totalQingliangStoreQuota   Int?
  parentUserId               String?
  extField1                  String?
  extField2                  String?
  extField3                  String?
  extField4                  String?
  extField5                  String?
  extField6                  String?
  extField7                  String?
  extField8                  String?
  extField9                  String?
  extField10                 String?
  createdAt                  DateTime  @default(now())
  updatedAt                  DateTime  @updatedAt
  accounts                   Account[]
  parentUser                 User?     @relation("UserHierarchy", fields: [parentUserId], references: [id])
  subUsers                   User[]    @relation("UserHierarchy")
}

model Account {
  id         String      @id @default(uuid())
  userId     String
  name       String
  type       AccountType
  extField1  String?
  extField2  String?
  extField3  String?
  extField4  String?
  extField5  String?
  extField6  String?
  extField7  String?
  extField8  String?
  extField9  String?
  extField10 String?
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  user       User        @relation(fields: [userId], references: [id])
  stores     Store[]
}

model Store {
  id         String   @id @default(uuid())
  accountId  String
  name       String
  shortName  String?
  extField1  String?
  extField2  String?
  extField3  String?
  extField4  String?
  extField5  String?
  extField6  String?
  extField7  String?
  extField8  String?
  extField9  String?
  extField10 String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  account    Account  @relation(fields: [accountId], references: [id])
}

model KeyvalueInfo {
  id         String     @id @default(uuid())
  name       String
  key        String     @unique
  value      String?
  prekey     String?
  type       String?
  accessType AccessType @default(PRIVATE)
  extField1  String?
  extField2  String?
  extField3  String?
  extField4  String?
  extField5  String?
  extField6  String?
  extField7  String?
  extField8  String?
  extField9  String?
  extField10 String?
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
}
