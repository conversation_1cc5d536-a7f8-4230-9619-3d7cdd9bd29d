import { ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthGuard } from '@nestjs/passport';
import { IS_PUBLIC_KEY } from '../keyvalue/decorators/public.decorator';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

@Injectable()
export class JwtAuthGuard extends AuthGuard('jwt') {
  constructor(private reflector: Reflector) {
    super();
  }

  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    // 获取请求对象
    const request = context.switchToHttp().getRequest();
    const { path } = request.route;

    // 调试日志：打印请求头信息
    console.log('请求路径:', path);
    console.log('Authorization 头:', request.headers.authorization);

    // 自动识别登录和注册接口
    const isAuthPath = path === '/auth/login';

    // 检查是否标记为公开接口
    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果是公开接口或登录/注册接口，尝试解析token但不强制要求
    if (isPublic || isAuthPath) {
      try {
        // 尝试解析token，但忽略错误
        const canActivate = super.canActivate(context);
        if (canActivate instanceof Observable) {
          return canActivate.pipe(
            tap({
              error: () => {
                // 忽略错误，公开接口不需要强制认证
                return true;
              }
            })
          );
        }
        return canActivate;
      } catch (error) {
        // 忽略错误，公开接口不需要强制认证
        return true;
      }
    }

    // 否则执行JWT认证并强制要求
    return super.canActivate(context);
  }

  // 重写handleRequest方法，在公开接口中不抛出异常
  handleRequest(err, user, info, context) {
    // 获取请求对象
    const request = context.switchToHttp().getRequest();
    const { path } = request.route;

    // 自动识别登录和注册接口
    const isAuthPath = path === '/auth/login';

    const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    // 如果是公开接口或登录/注册接口且没有用户（未认证），返回null而不是抛出异常
    if ((isPublic || isAuthPath) && !user) {
      return null;
    }

    // 否则使用默认行为
    return super.handleRequest(err, user, info, context);
  }
}
