# 轻量后端API系统接口说明文档

## 概述

本文档描述了基于NestJS + Prisma + SQLite + JWT的轻量级后端API系统的接口。系统提供了用户认证、账号管理和门店管理等功能。

## 基本信息

- 基础URL: `http://localhost:3000`
- Swagger文档: `http://localhost:3000/api-docs`
- 认证方式: JWT Bearer Token

## 认证

### 登录

- **URL**: `/auth/login`
- **方法**: `POST`
- **描述**: 验证用户名和密码，返回JWT令牌
- **请求体**:
  ```json
  {
    "username": "user",
    "password": "user123"
  }
  ```
- **成功响应** (200):
  ```json
  {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "a913cd9e-c5ad-47fe-b87f-4fab7c545d40",
      "username": "user",
      "role": "CUSTOMER",
      "phone": "***********",
      "email": "<EMAIL>",
      "company": "测试公司",
      "createdAt": "2025-04-24T03:45:28.308Z",
      "updatedAt": "2025-04-24T03:45:28.308Z"
    }
  }
  ```
- **错误响应** (401):
  ```json
  {
    "statusCode": 401,
    "message": "用户名或密码错误",
    "error": "Unauthorized"
  }
  ```

## 用户管理

### 创建用户

- **URL**: `/users`
- **方法**: `POST`
- **描述**: 创建新用户
- **请求体**:
  ```json
  {
    "username": "newuser",
    "password": "password123",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "company": "新公司",
    "role": "CUSTOMER"
  }
  ```
- **成功响应** (201):
  ```json
  {
    "id": "b913cd9e-c5ad-47fe-b87f-4fab7c545d41",
    "username": "newuser",
    "role": "CUSTOMER",
    "phone": "13800138000",
    "email": "<EMAIL>",
    "company": "新公司",
    "createdAt": "2025-04-24T10:00:00.000Z",
    "updatedAt": "2025-04-24T10:00:00.000Z"
  }
  ```
- **错误响应** (409):
  ```json
  {
    "statusCode": 409,
    "message": "用户名已存在",
    "error": "Conflict"
  }
  ```

### 获取当前用户信息

- **URL**: `/users/profile`
- **方法**: `GET`
- **描述**: 获取当前登录用户的信息
- **认证**: 需要JWT令牌
- **成功响应** (200):
  ```json
  {
    "id": "a913cd9e-c5ad-47fe-b87f-4fab7c545d40",
    "username": "user",
    "role": "CUSTOMER",
    "phone": "***********",
    "email": "<EMAIL>",
    "company": "测试公司",
    "createdAt": "2025-04-24T03:45:28.308Z",
    "updatedAt": "2025-04-24T03:45:28.308Z"
  }
  ```
- **错误响应** (401):
  ```json
  {
    "statusCode": 401,
    "message": "Unauthorized"
  }
  ```

## 账号管理

### 创建账号

- **URL**: `/accounts`
- **方法**: `POST`
- **描述**: 为当前登录用户创建新账号
- **认证**: 需要JWT令牌
- **请求体**:
  ```json
  {
    "name": "新抖音账号",
    "type": "DOUYIN"
  }
  ```
- **成功响应** (201):
  ```json
  {
    "id": "c913cd9e-c5ad-47fe-b87f-4fab7c545d42",
    "userId": "a913cd9e-c5ad-47fe-b87f-4fab7c545d40",
    "name": "新抖音账号",
    "type": "DOUYIN",
    "createdAt": "2025-04-24T10:00:00.000Z",
    "updatedAt": "2025-04-24T10:00:00.000Z"
  }
  ```
- **错误响应** (401):
  ```json
  {
    "statusCode": 401,
    "message": "Unauthorized"
  }
  ```

### 获取账号列表

- **URL**: `/accounts`
- **方法**: `GET`
- **描述**: 获取当前登录用户的所有账号
- **认证**: 需要JWT令牌
- **成功响应** (200):
  ```json
  [
    {
      "id": "079471b8-1e78-4fbb-8425-d9ca8c4c503d",
      "userId": "a913cd9e-c5ad-47fe-b87f-4fab7c545d40",
      "name": "抖音账号1",
      "type": "DOUYIN",
      "createdAt": "2025-04-24T03:45:28.308Z",
      "updatedAt": "2025-04-24T03:45:28.308Z"
    },
    {
      "id": "ad699c63-1431-4c98-9bd5-fd99f031c845",
      "userId": "a913cd9e-c5ad-47fe-b87f-4fab7c545d40",
      "name": "轻量账号1",
      "type": "QINGLIANG",
      "createdAt": "2025-04-24T03:45:28.311Z",
      "updatedAt": "2025-04-24T03:45:28.311Z"
    }
  ]
  ```
- **错误响应** (401):
  ```json
  {
    "statusCode": 401,
    "message": "Unauthorized"
  }
  ```

### 获取账号和门店列表

- **URL**: `/accounts/with-stores`
- **方法**: `GET`
- **描述**: 获取当前登录用户的所有账号及其关联的门店
- **认证**: 需要JWT令牌
- **成功响应** (200):
  ```json
  {
    "user": {
      "id": "a913cd9e-c5ad-47fe-b87f-4fab7c545d40",
      "username": "user",
      "role": "CUSTOMER",
      "company": "测试公司",
      "phone": "***********",
      "email": "<EMAIL>"
    },
    "accounts": [
      {
        "id": "079471b8-1e78-4fbb-8425-d9ca8c4c503d",
        "userId": "a913cd9e-c5ad-47fe-b87f-4fab7c545d40",
        "name": "抖音账号1",
        "type": "DOUYIN",
        "createdAt": "2025-04-24T03:45:28.308Z",
        "updatedAt": "2025-04-24T03:45:28.308Z",
        "stores": [
          {
            "id": "98a36b40-a5b4-47fa-838d-4eb4705f3557",
            "accountId": "079471b8-1e78-4fbb-8425-d9ca8c4c503d",
            "name": "抖音门店1",
            "shortName": "抖1",
            "createdAt": "2025-04-24T03:45:28.313Z",
            "updatedAt": "2025-04-24T03:45:28.313Z"
          },
          {
            "id": "b90dce3d-4970-4dc7-b93f-d33afbe799de",
            "accountId": "079471b8-1e78-4fbb-8425-d9ca8c4c503d",
            "name": "抖音门店2",
            "shortName": "抖2",
            "createdAt": "2025-04-24T03:45:28.315Z",
            "updatedAt": "2025-04-24T03:45:28.315Z"
          }
        ]
      },
      {
        "id": "ad699c63-1431-4c98-9bd5-fd99f031c845",
        "userId": "a913cd9e-c5ad-47fe-b87f-4fab7c545d40",
        "name": "轻量账号1",
        "type": "QINGLIANG",
        "createdAt": "2025-04-24T03:45:28.311Z",
        "updatedAt": "2025-04-24T03:45:28.311Z",
        "stores": [
          {
            "id": "2712ad52-7ca4-45ed-80e8-f0d4f29bb6d6",
            "accountId": "ad699c63-1431-4c98-9bd5-fd99f031c845",
            "name": "轻量门店1",
            "shortName": "轻1",
            "createdAt": "2025-04-24T03:45:28.318Z",
            "updatedAt": "2025-04-24T03:45:28.318Z"
          }
        ]
      }
    ]
  }
  ```
- **错误响应** (401):
  ```json
  {
    "statusCode": 401,
    "message": "Unauthorized"
  }
  ```

## 门店管理

### 创建门店

- **URL**: `/stores`
- **方法**: `POST`
- **描述**: 为指定账号创建新门店
- **认证**: 需要JWT令牌
- **请求体**:
  ```json
  {
    "accountId": "079471b8-1e78-4fbb-8425-d9ca8c4c503d",
    "name": "新抖音门店",
    "shortName": "新抖"
  }
  ```
- **成功响应** (201):
  ```json
  {
    "id": "d913cd9e-c5ad-47fe-b87f-4fab7c545d43",
    "accountId": "079471b8-1e78-4fbb-8425-d9ca8c4c503d",
    "name": "新抖音门店",
    "shortName": "新抖",
    "createdAt": "2025-04-24T10:00:00.000Z",
    "updatedAt": "2025-04-24T10:00:00.000Z"
  }
  ```
- **错误响应** (401):
  ```json
  {
    "statusCode": 401,
    "message": "Unauthorized"
  }
  ```
- **错误响应** (403):
  ```json
  {
    "statusCode": 403,
    "message": "无权操作此账号",
    "error": "Forbidden"
  }
  ```

### 获取账号下的门店列表

- **URL**: `/stores/by-account/:accountId`
- **方法**: `GET`
- **描述**: 获取指定账号下的所有门店
- **认证**: 需要JWT令牌
- **参数**:
  - `accountId`: 账号ID
- **成功响应** (200):
  ```json
  [
    {
      "id": "98a36b40-a5b4-47fa-838d-4eb4705f3557",
      "accountId": "079471b8-1e78-4fbb-8425-d9ca8c4c503d",
      "name": "抖音门店1",
      "shortName": "抖1",
      "createdAt": "2025-04-24T03:45:28.313Z",
      "updatedAt": "2025-04-24T03:45:28.313Z"
    },
    {
      "id": "b90dce3d-4970-4dc7-b93f-d33afbe799de",
      "accountId": "079471b8-1e78-4fbb-8425-d9ca8c4c503d",
      "name": "抖音门店2",
      "shortName": "抖2",
      "createdAt": "2025-04-24T03:45:28.315Z",
      "updatedAt": "2025-04-24T03:45:28.315Z"
    }
  ]
  ```
- **错误响应** (401):
  ```json
  {
    "statusCode": 401,
    "message": "Unauthorized"
  }
  ```
- **错误响应** (403):
  ```json
  {
    "statusCode": 403,
    "message": "无权查看此账号的门店",
    "error": "Forbidden"
  }
  ```

## 数据模型

### 用户表 (User)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | String | 用户UUID，主键 |
| username | String | 用户名，唯一 |
| password | String | 密码（MD5加密） |
| phone | String? | 手机号码 |
| email | String? | 电子邮箱 |
| company | String? | 公司名称 |
| role | UserRole | 用户角色（SUPER_ADMIN/CUSTOMER） |
| extField1-10 | String? | 文本扩展字段1-10 |
| createdAt | DateTime | 创建时间 |
| updatedAt | DateTime | 更新时间 |

### 账号表 (Account)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | String | 账号UUID，主键 |
| userId | String | 所属用户UUID，外键 |
| name | String | 账号名称 |
| type | AccountType | 账号类型（DOUYIN/QINGLIANG） |
| extField1-10 | String? | 文本扩展字段1-10 |
| createdAt | DateTime | 创建时间 |
| updatedAt | DateTime | 更新时间 |

### 门店表 (Store)

| 字段名 | 类型 | 描述 |
|-------|------|------|
| id | String | 门店UUID，主键 |
| accountId | String | 所属账号UUID，外键 |
| name | String | 门店名称 |
| shortName | String? | 门店短名称 |
| extField1-10 | String? | 文本扩展字段1-10 |
| createdAt | DateTime | 创建时间 |
| updatedAt | DateTime | 更新时间 |

## 枚举类型

### 用户角色 (UserRole)

- `SUPER_ADMIN`: 超级管理员
- `CUSTOMER`: 普通客户账号

### 账号类型 (AccountType)

- `DOUYIN`: 抖音账号
- `QINGLIANG`: 轻量账号

## 测试账号

系统已预置以下测试账号：

1. 超级管理员
   - 用户名: admin
   - 密码: admin123

2. 普通用户
   - 用户名: user
   - 密码: user123
