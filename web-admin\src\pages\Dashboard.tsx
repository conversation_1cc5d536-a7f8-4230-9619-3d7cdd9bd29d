import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, Alert, Typography, Space, Spin, Button } from 'antd';
import {
  ShopOutlined,
  TeamOutlined,
  DatabaseOutlined,
  WarningOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import { webService, type DashboardData } from '../services/web';
import { eventBus, EVENTS } from '../utils/eventBus';

const { Title } = Typography;

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<DashboardData | null>(null);

  useEffect(() => {
    fetchDashboardData();

    // 页面获得焦点时刷新数据
    const handleFocus = () => {
      fetchDashboardData();
    };

    // 监听数据清空事件
    const handleDataCleared = () => {
      console.log('收到数据清空事件，刷新 Dashboard 数据');
      fetchDashboardData();
    };

    // 监听仪表板刷新事件
    const handleDashboardRefresh = () => {
      console.log('收到仪表板刷新事件');
      fetchDashboardData();
    };

    window.addEventListener('focus', handleFocus);
    eventBus.on(EVENTS.DATA_CLEARED, handleDataCleared);
    eventBus.on(EVENTS.DASHBOARD_REFRESH, handleDashboardRefresh);

    // 设置定时刷新（每5分钟刷新一次）
    const interval = setInterval(fetchDashboardData, 5 * 60 * 1000);

    return () => {
      window.removeEventListener('focus', handleFocus);
      eventBus.off(EVENTS.DATA_CLEARED, handleDataCleared);
      eventBus.off(EVENTS.DASHBOARD_REFRESH, handleDashboardRefresh);
      clearInterval(interval);
    };
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      const dashboardData = await webService.getDashboard();
      setData(dashboardData);
    } catch (error) {
      console.error('获取仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!data) {
    return (
      <Alert
        message="数据加载失败"
        description="无法加载仪表板数据，请刷新页面重试"
        type="error"
        showIcon
      />
    );
  }

  const { quotaStats, quotaLimits, isWithinLimit, exceededTypes, subUserCount } = data;

  const getUsagePercent = (used: number, total: number) => {
    if (total === 0) return 0; // 无限制时显示0%
    return Math.round((used / total) * 100);
  };

  const getProgressStatus = (used: number, total: number) => {
    if (total === 0) return 'normal'; // 无限制时显示正常状态
    const percent = getUsagePercent(used, total);
    if (percent >= 100) return 'exception';
    if (percent >= 80) return 'active';
    return 'normal';
  };

  const formatQuotaDisplay = (used: number, total: number) => {
    return total === 0 ? `${used} / 无限制` : `${used} / ${total}`;
  };

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>仪表板</Title>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={fetchDashboardData}
          loading={loading}
        >
          刷新数据
        </Button>
      </div>
      
      {!isWithinLimit && (
        <Alert
          message="配额超限警告"
          description={`以下类型已超出配额限制：${exceededTypes.join('、')}`}
          type="error"
          icon={<WarningOutlined />}
          showIcon
          closable
          style={{ marginBottom: 24 }}
        />
      )}

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="子账号数量"
              value={subUserCount}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="抖店账号总数"
              value={quotaStats.douyinAccountCount}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix={`/ ${quotaLimits.totalDouyinAccountQuota === 0 ? '无限制' : quotaLimits.totalDouyinAccountQuota}`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="清凉账号总数"
              value={quotaStats.qingliangAccountCount}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#722ed1' }}
              suffix={`/ ${quotaLimits.totalQingliangAccountQuota === 0 ? '无限制' : quotaLimits.totalQingliangAccountQuota}`}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="门店总数"
              value={quotaStats.douyinStoreCount + quotaStats.qingliangStoreCount}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#fa8c16' }}
              suffix={`/ ${(quotaLimits.totalDouyinStoreQuota === 0 && quotaLimits.totalQingliangStoreQuota === 0) ? '无限制' : (quotaLimits.totalDouyinStoreQuota + quotaLimits.totalQingliangStoreQuota)}`}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="抖店配额使用情况" extra={<DatabaseOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <div>
                <div style={{ marginBottom: 8 }}>
                  <span>账号使用量</span>
                  <span style={{ float: 'right' }}>
                    {formatQuotaDisplay(quotaStats.douyinAccountCount, quotaLimits.totalDouyinAccountQuota)}
                  </span>
                </div>
                <Progress
                  percent={getUsagePercent(quotaStats.douyinAccountCount, quotaLimits.totalDouyinAccountQuota)}
                  status={getProgressStatus(quotaStats.douyinAccountCount, quotaLimits.totalDouyinAccountQuota)}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>
              <div>
                <div style={{ marginBottom: 8 }}>
                  <span>门店使用量</span>
                  <span style={{ float: 'right' }}>
                    {formatQuotaDisplay(quotaStats.douyinStoreCount, quotaLimits.totalDouyinStoreQuota)}
                  </span>
                </div>
                <Progress
                  percent={getUsagePercent(quotaStats.douyinStoreCount, quotaLimits.totalDouyinStoreQuota)}
                  status={getProgressStatus(quotaStats.douyinStoreCount, quotaLimits.totalDouyinStoreQuota)}
                  strokeColor={{
                    '0%': '#108ee9',
                    '100%': '#87d068',
                  }}
                />
              </div>
            </Space>
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card title="清凉配额使用情况" extra={<ShopOutlined />}>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <div>
                <div style={{ marginBottom: 8 }}>
                  <span>账号使用量</span>
                  <span style={{ float: 'right' }}>
                    {formatQuotaDisplay(quotaStats.qingliangAccountCount, quotaLimits.totalQingliangAccountQuota)}
                  </span>
                </div>
                <Progress
                  percent={getUsagePercent(quotaStats.qingliangAccountCount, quotaLimits.totalQingliangAccountQuota)}
                  status={getProgressStatus(quotaStats.qingliangAccountCount, quotaLimits.totalQingliangAccountQuota)}
                  strokeColor={{
                    '0%': '#722ed1',
                    '100%': '#eb2f96',
                  }}
                />
              </div>
              <div>
                <div style={{ marginBottom: 8 }}>
                  <span>门店使用量</span>
                  <span style={{ float: 'right' }}>
                    {formatQuotaDisplay(quotaStats.qingliangStoreCount, quotaLimits.totalQingliangStoreQuota)}
                  </span>
                </div>
                <Progress
                  percent={getUsagePercent(quotaStats.qingliangStoreCount, quotaLimits.totalQingliangStoreQuota)}
                  status={getProgressStatus(quotaStats.qingliangStoreCount, quotaLimits.totalQingliangStoreQuota)}
                  strokeColor={{
                    '0%': '#722ed1',
                    '100%': '#eb2f96',
                  }}
                />
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;