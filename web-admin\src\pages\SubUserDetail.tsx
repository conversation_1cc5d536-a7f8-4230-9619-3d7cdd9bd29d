import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Table,
  Tag,
  Button,
  Space,
  Typography,
  Spin,
  Alert,
  Collapse,
  Statistic,
  Row,
  Col,
} from 'antd';
import {
  ArrowLeftOutlined,
  UserOutlined,
  ShopOutlined,
  DatabaseOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { webService, type SubUser } from '../services/web';
import dayjs from 'dayjs';

const { Title } = Typography;
const { Panel } = Collapse;

interface StoreData {
  id: string;
  name: string;
  shortName?: string;
  createdAt: string;
  accountName: string;
  accountType: string;
}

const SubUserDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [subUser, setSubUser] = useState<SubUser | null>(null);

  useEffect(() => {
    if (id) {
      fetchSubUserDetail(id);
    }
  }, [id]);

  const fetchSubUserDetail = async (userId: string) => {
    try {
      setLoading(true);
      const data = await webService.getSubUserDetails(userId);
      setSubUser(data);
    } catch (error) {
      console.error('获取子账号详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStoreData = (): StoreData[] => {
    if (!subUser) return [];
    
    const stores: StoreData[] = [];
    subUser.accounts.forEach(account => {
      account.stores.forEach(store => {
        stores.push({
          ...store,
          accountName: account.name,
          accountType: account.type,
        });
      });
    });
    
    return stores;
  };

  const storeColumns: ColumnsType<StoreData> = [
    {
      title: '门店名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <Space>
          <ShopOutlined />
          <strong>{text}</strong>
        </Space>
      ),
    },
    {
      title: '门店简称',
      dataIndex: 'shortName',
      key: 'shortName',
      render: (text: string) => text || '-',
    },
    {
      title: '所属账号',
      dataIndex: 'accountName',
      key: 'accountName',
    },
    {
      title: '账号类型',
      dataIndex: 'accountType',
      key: 'accountType',
      render: (type: string) => (
        <Tag color={type === 'DOUYIN' ? 'blue' : 'purple'}>
          {type === 'DOUYIN' ? '抖店' : '清凉'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
  ];

  const accountColumns: ColumnsType<SubUser['accounts'][0]> = [
    {
      title: '账号名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <Space>
          <DatabaseOutlined />
          <strong>{text}</strong>
        </Space>
      ),
    },
    {
      title: '账号类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => (
        <Tag color={type === 'DOUYIN' ? 'blue' : 'purple'}>
          {type === 'DOUYIN' ? '抖店' : '清凉'}
        </Tag>
      ),
    },
    {
      title: '门店数量',
      key: 'storeCount',
      render: (_, record) => (
        <Tag color="green" icon={<ShopOutlined />}>
          {record.stores.length}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!subUser) {
    return (
      <Alert
        message="数据加载失败"
        description="无法加载子账号详情，请返回重试"
        type="error"
        showIcon
        action={
          <Button onClick={() => navigate('/web/sub-users')}>
            返回列表
          </Button>
        }
      />
    );
  }

  return (
    <div>
      <Space style={{ marginBottom: 16 }}>
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/web/sub-users')}
        >
          返回
        </Button>
        <Title level={2} style={{ margin: 0 }}>
          子账号详情
        </Title>
      </Space>

      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="抖店账号"
              value={subUser.stats.douyinAccountCount}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="清凉账号"
              value={subUser.stats.qingliangAccountCount}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="抖店门店"
              value={subUser.stats.douyinStoreCount}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="清凉门店"
              value={subUser.stats.qingliangStoreCount}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      <Space direction="vertical" style={{ width: '100%' }} size="large">
        <Card title="基本信息" extra={<UserOutlined />}>
          <Descriptions column={2}>
            <Descriptions.Item label="用户名">{subUser.username}</Descriptions.Item>
            <Descriptions.Item label="角色">{subUser.role}</Descriptions.Item>
            <Descriptions.Item label="手机号码">{subUser.phone || '-'}</Descriptions.Item>
            <Descriptions.Item label="邮箱">{subUser.email || '-'}</Descriptions.Item>
            <Descriptions.Item label="公司">{subUser.company || '-'}</Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {dayjs(subUser.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
          </Descriptions>
        </Card>

        <Collapse defaultActiveKey={['accounts', 'stores']}>
          <Panel header={`账号列表 (${subUser.accounts.length})`} key="accounts">
            <Table
              columns={accountColumns}
              dataSource={subUser.accounts}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Panel>
          
          <Panel header={`门店列表 (${getStoreData().length})`} key="stores">
            <Table
              columns={storeColumns}
              dataSource={getStoreData()}
              rowKey="id"
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => 
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              size="small"
            />
          </Panel>
        </Collapse>
      </Space>
    </div>
  );
};

export default SubUserDetail;