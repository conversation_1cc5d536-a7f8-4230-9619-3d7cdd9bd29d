import { Injectable, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class StoreService {
  constructor(private readonly prismaService: PrismaService) {}

  async create(data: {
    accountId: string;
    name: string;
    shortName?: string;
    userId: string; // 用于验证权限
  }) {
    // 验证账号是否属于当前用户
    const account = await this.prismaService.account.findUnique({
      where: { id: data.accountId },
    });

    if (!account || account.userId !== data.userId) {
      throw new ForbiddenException('无权操作此账号');
    }

    // 创建门店
    return this.prismaService.store.create({
      data: {
        accountId: data.accountId,
        name: data.name,
        shortName: data.shortName,
      },
    });
  }

  async findByAccountId(accountId: string, userId: string) {
    // 验证账号是否属于当前用户
    const account = await this.prismaService.account.findUnique({
      where: { id: accountId },
    });

    if (!account || account.userId !== userId) {
      throw new ForbiddenException('无权查看此账号的门店');
    }

    return this.prismaService.store.findMany({
      where: { accountId },
    });
  }
}
