# Swagger UI 认证问题解决方案

## 问题描述

在使用 Swagger UI 测试 API 接口时，虽然在顶部的 Authorize 按钮中设置了 JWT token，但在实际发送请求时，请求中并没有包含 Authorization 头信息，导致接口返回 401 Unauthorized 错误。

## 问题分析

经过分析，发现以下几个可能的原因：

1. **Swagger UI 配置问题**：缺少 `persistAuthorization: true` 等关键配置选项
2. **控制器装饰器不一致**：`@ApiBearerAuth()` 装饰器参数不一致或缺失
3. **JWT 认证守卫实现问题**：可能在处理 Swagger 请求时存在问题
4. **浏览器缓存问题**：可能是浏览器缓存导致的问题

## 解决方案

### 1. 添加 Swagger UI 高级配置

在 `main.ts` 文件中，添加 Swagger UI 的高级配置，特别是 `persistAuthorization: true` 选项，这可以确保授权信息在页面刷新后仍然保持：

```typescript
// 原代码
const document = SwaggerModule.createDocument(app, config);
SwaggerModule.setup('api-docs', app, document);

// 修改后的代码
const document = SwaggerModule.createDocument(app, config);

// 添加Swagger UI的高级配置
const customOptions = {
  swaggerOptions: {
    persistAuthorization: true, // 保持授权状态
    tryItOutEnabled: true,      // 默认启用"Try it out"功能
    displayRequestDuration: true, // 显示请求持续时间
    filter: true,               // 启用过滤功能
    syntaxHighlight: {          // 语法高亮
      activate: true,
      theme: 'agate'
    },
  },
  customSiteTitle: '后端API系统文档', // 自定义网页标题
};

SwaggerModule.setup('api-docs', app, document, customOptions);
```

### 2. 统一 `@ApiBearerAuth()` 装饰器参数

确保所有控制器中的 `@ApiBearerAuth()` 装饰器都使用相同的参数，与 `main.ts` 中的配置一致：

```typescript
// main.ts 中的配置
.addBearerAuth(
  {
    type: 'http',
    scheme: 'bearer',
    bearerFormat: 'JWT',
    name: 'Authorization',
    description: '输入JWT token',
    in: 'header'
  },
  'access-token', // 这个名称用于在Swagger UI中引用
)

// 控制器中的装饰器应该使用相同的名称
@ApiTags('用户')
@ApiBearerAuth('access-token')  // 使用相同的名称
@Controller('users')
export class UserController {
  // ...
}
```

修改了以下控制器中的装饰器：

- `UserController`
- `AccountController`
- `StoreController`
- `KeyvalueController`
- `ImportController`

### 3. 添加调试日志

在 `JwtAuthGuard` 中添加调试日志，以便在出现问题时能够更容易地排查：

```typescript
canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
  // 获取请求对象
  const request = context.switchToHttp().getRequest();
  const { path } = request.route;
  
  // 调试日志：打印请求头信息
  console.log('请求路径:', path);
  console.log('Authorization 头:', request.headers.authorization);

  // 其他代码...
}
```

### 4. 添加测试控制器

创建一个专门用于测试认证的控制器，以便验证 JWT 认证是否正常工作：

```typescript
import { Controller, Get, UseGuards, Request } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from './auth/jwt-auth.guard';

@ApiTags('测试认证')
@ApiBearerAuth('access-token')
@Controller('test-auth')
export class TestAuthController {
  @ApiOperation({ summary: '测试JWT认证', description: '测试JWT认证是否正常工作' })
  @ApiResponse({ status: 200, description: '认证成功' })
  @ApiResponse({ status: 401, description: '未授权' })
  @UseGuards(JwtAuthGuard)
  @Get()
  test(@Request() req) {
    // 打印请求头信息
    console.log('测试认证 - 请求头:', req.headers);
    console.log('测试认证 - 用户信息:', req.user);
    
    return {
      message: '认证成功',
      user: req.user,
      headers: {
        authorization: req.headers.authorization
      }
    };
  }
}
```

并将其添加到 `AppModule` 中：

```typescript
@Module({
  imports: [
    // ...其他模块
  ],
  controllers: [AppController, TestAuthController],
  providers: [AppService],
})
export class AppModule {}
```

## 使用方法

完成上述修改后，按照以下步骤使用 Swagger UI：

1. 重启应用程序：`npm run start:dev`
2. 访问 Swagger UI 页面：`http://localhost:3000/api-docs`
3. 点击右上角的 "Authorize" 按钮
4. 在弹出的对话框中输入 JWT token（**不要**包含 "Bearer " 前缀）
5. 点击 "Authorize" 按钮
6. 点击 "Close" 关闭对话框
7. 尝试调用需要授权的 API 接口

## 注意事项

1. 确保输入的 token 格式正确，不要包含 "Bearer " 前缀
2. 如果问题仍然存在，可以尝试清除浏览器缓存或使用隐私模式
3. 查看控制台日志，特别是添加的调试日志，以获取更多信息
4. 确保 JWT token 未过期且有效

## 总结

解决 Swagger UI 认证问题的关键在于：

1. 添加 `persistAuthorization: true` 配置选项
2. 确保所有控制器中的 `@ApiBearerAuth()` 装饰器参数一致
3. 正确配置 JWT 认证守卫
4. 在 Authorize 对话框中正确输入 token（不包含 "Bearer " 前缀）

通过这些修改，Swagger UI 现在可以正确地在请求中包含 Authorization 头信息，从而成功通过 JWT 认证。
