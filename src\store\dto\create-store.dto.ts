import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateStoreDto {
  @ApiProperty({ description: '所属账号ID', example: '079471b8-1e78-4fbb-8425-d9ca8c4c503d' })
  @IsUUID()
  accountId: string;

  @ApiProperty({ description: '门店名称', example: '抖音门店1' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: '门店短名称', example: '抖1' })
  @IsOptional()
  @IsString()
  shortName?: string;
}
