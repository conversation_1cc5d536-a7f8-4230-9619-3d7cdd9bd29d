import { Module } from '@nestjs/common';
import { ImportService } from './import.service';
import { ImportController } from './import.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { AccountModule } from '../account/account.module';
import { StoreModule } from '../store/store.module';
import { QuotaModule } from '../quota/quota.module';

@Module({
  imports: [PrismaModule, AccountModule, StoreModule, QuotaModule],
  controllers: [ImportController],
  providers: [ImportService],
  exports: [ImportService], // 导出ImportService供其他模块使用
})
export class ImportModule {}
