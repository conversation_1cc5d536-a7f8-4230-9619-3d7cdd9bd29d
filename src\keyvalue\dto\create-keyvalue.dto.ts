import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { AccessType } from '@prisma/client';

export class CreateKeyvalueDto {
  @ApiProperty({ description: '名称', example: '系统配置' })
  @IsString()
  name: string;

  @ApiProperty({ description: '唯一键', example: 'system.config' })
  @IsString()
  key: string;

  @ApiPropertyOptional({ description: '值', example: '{"theme":"dark"}' })
  @IsOptional()
  @IsString()
  value?: string;

  @ApiPropertyOptional({ description: '父级键', example: 'system' })
  @IsOptional()
  @IsString()
  prekey?: string;

  @ApiPropertyOptional({ description: '类型', example: 'config' })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({ 
    description: '访问权限', 
    enum: AccessType,
    default: AccessType.PRIVATE,
    example: AccessType.PUBLIC
  })
  @IsOptional()
  @IsEnum(AccessType)
  accessType?: AccessType;

  @ApiPropertyOptional({ description: '扩展字段1', example: '扩展信息1' })
  @IsOptional()
  @IsString()
  extField1?: string;

  @ApiPropertyOptional({ description: '扩展字段2', example: '扩展信息2' })
  @IsOptional()
  @IsString()
  extField2?: string;

  @ApiPropertyOptional({ description: '扩展字段3', example: '扩展信息3' })
  @IsOptional()
  @IsString()
  extField3?: string;

  @ApiPropertyOptional({ description: '扩展字段4', example: '扩展信息4' })
  @IsOptional()
  @IsString()
  extField4?: string;

  @ApiPropertyOptional({ description: '扩展字段5', example: '扩展信息5' })
  @IsOptional()
  @IsString()
  extField5?: string;

  @ApiPropertyOptional({ description: '扩展字段6', example: '扩展信息6' })
  @IsOptional()
  @IsString()
  extField6?: string;

  @ApiPropertyOptional({ description: '扩展字段7', example: '扩展信息7' })
  @IsOptional()
  @IsString()
  extField7?: string;

  @ApiPropertyOptional({ description: '扩展字段8', example: '扩展信息8' })
  @IsOptional()
  @IsString()
  extField8?: string;

  @ApiPropertyOptional({ description: '扩展字段9', example: '扩展信息9' })
  @IsOptional()
  @IsString()
  extField9?: string;

  @ApiPropertyOptional({ description: '扩展字段10', example: '扩展信息10' })
  @IsOptional()
  @IsString()
  extField10?: string;
}
