import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class QueryByKeyDto {
  @ApiProperty({ description: '唯一键', example: 'system.config' })
  @IsString()
  key: string;
}

export class QueryByPrekeyDto {
  @ApiProperty({ description: '父级键', example: 'system' })
  @IsString()
  prekey: string;
}

export class QueryByTypeDto {
  @ApiProperty({ description: '类型', example: 'config' })
  @IsString()
  type: string;
}
