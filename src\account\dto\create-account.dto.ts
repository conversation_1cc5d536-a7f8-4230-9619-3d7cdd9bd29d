import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsString } from 'class-validator';
import { AccountType } from '@prisma/client';

export class CreateAccountDto {
  @ApiProperty({ description: '账号名称', example: '抖音账号1' })
  @IsString()
  name: string;

  @ApiProperty({ 
    description: '账号类型', 
    enum: AccountType,
    example: AccountType.DOUYIN
  })
  @IsEnum(AccountType)
  type: AccountType;
}
