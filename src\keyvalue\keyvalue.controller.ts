import { Controller, Get, Post, Body, Param, Put, Delete, UseGuards, Request, Query } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { KeyvalueService } from './keyvalue.service';
import { CreateKeyvalueDto } from './dto/create-keyvalue.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Public } from './decorators/public.decorator';

@ApiTags('键值信息')
@Controller('keyvalue')
export class KeyvalueController {
  constructor(private readonly keyvalueService: KeyvalueService) {}

  @ApiOperation({ summary: '创建键值信息', description: '创建新的键值信息' })
  @ApiResponse({ status: 201, description: '键值信息创建成功' })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiBearerAuth('access-token')
  @UseGuards(JwtAuthGuard)
  @Post()
  create(@Body() createKeyvalueDto: CreateKeyvalueDto) {
    return this.keyvalueService.create(createKeyvalueDto);
  }

  @ApiOperation({ summary: '通过key查询信息', description: '根据唯一键查询信息' })
  @ApiParam({ name: 'key', description: '唯一键' })
  @ApiResponse({ status: 200, description: '成功获取信息' })
  @ApiResponse({ status: 404, description: '信息不存在或无权访问' })
  @ApiBearerAuth('access-token')
  @Get('by-key/:key')
  findByKey(@Param('key') key: string, @Request() req) {
    // 检查请求头中是否包含Authorization
    const authHeader = req.headers.authorization;
    const isAuthenticated = !!authHeader && authHeader.startsWith('Bearer ');
    console.log('认证状态:', isAuthenticated, '认证头:', authHeader);
    return this.keyvalueService.findByKey(key, isAuthenticated);
  }

  @ApiOperation({ summary: '通过prekey查询子项', description: '根据父级键查询所有子项' })
  @ApiParam({ name: 'prekey', description: '父级键' })
  @ApiResponse({ status: 200, description: '成功获取子项列表' })
  @ApiBearerAuth('access-token')
  @Get('by-prekey/:prekey')
  findByPrekey(@Param('prekey') prekey: string, @Request() req) {
    // 检查请求头中是否包含Authorization
    const authHeader = req.headers.authorization;
    const isAuthenticated = !!authHeader && authHeader.startsWith('Bearer ');
    console.log('认证状态:', isAuthenticated, '认证头:', authHeader);
    return this.keyvalueService.findByPrekey(prekey, isAuthenticated);
  }

  @ApiOperation({ summary: '通过type查询列表', description: '根据类型查询列表' })
  @ApiParam({ name: 'type', description: '类型' })
  @ApiResponse({ status: 200, description: '成功获取列表' })
  @ApiBearerAuth('access-token')
  @Get('by-type/:type')
  findByType(@Param('type') type: string, @Request() req) {
    // 检查请求头中是否包含Authorization
    const authHeader = req.headers.authorization;
    const isAuthenticated = !!authHeader && authHeader.startsWith('Bearer ');
    console.log('认证状态:', isAuthenticated, '认证头:', authHeader);
    return this.keyvalueService.findByType(type, isAuthenticated);
  }

  @ApiOperation({ summary: '更新键值信息', description: '更新指定ID的键值信息' })
  @ApiParam({ name: 'id', description: '键值信息ID' })
  @ApiResponse({ status: 200, description: '键值信息更新成功' })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 404, description: '键值信息不存在' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Put(':id')
  update(@Param('id') id: string, @Body() updateKeyvalueDto: CreateKeyvalueDto) {
    return this.keyvalueService.update(id, updateKeyvalueDto);
  }

  @ApiOperation({ summary: '删除键值信息', description: '删除指定ID的键值信息' })
  @ApiParam({ name: 'id', description: '键值信息ID' })
  @ApiResponse({ status: 200, description: '键值信息删除成功' })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 404, description: '键值信息不存在' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.keyvalueService.remove(id);
  }
}
