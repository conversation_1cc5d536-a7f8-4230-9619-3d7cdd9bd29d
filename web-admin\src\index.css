:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: #213547;
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #1890ff;
  text-decoration: inherit;
}
a:hover {
  color: #40a9ff;
}

body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

/* 移除默认按钮样式，让 Ant Design 接管 */

/* 全局样式重置和优化 */
* {
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

/* 确保 Ant Design 组件正常显示 */
.ant-layout {
  background: #f0f2f5;
}

.ant-layout-sider {
  background: #001529 !important;
}

.ant-layout-header {
  background: #fff !important;
  border-bottom: 1px solid #f0f0f0;
}

.ant-layout-content {
  background: #f0f2f5 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ant-layout-content {
    margin: 16px 8px !important;
    padding: 16px !important;
  }

  .ant-card {
    margin-bottom: 16px;
  }

  .ant-col {
    margin-bottom: 16px;
  }
}

/* 确保表格在小屏幕上可以滚动 */
.ant-table-wrapper {
  overflow-x: auto;
}

/* 优化移动端菜单 */
@media (max-width: 992px) {
  .ant-layout-sider-collapsed {
    width: 0 !important;
    min-width: 0 !important;
  }
}
