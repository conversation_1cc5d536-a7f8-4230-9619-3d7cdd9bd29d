import { Controller, Post, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { Public } from '../keyvalue/decorators/public.decorator';

@ApiTags('认证')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @ApiOperation({ summary: '用户登录', description: '验证用户名和密码，返回JWT令牌' })
  @ApiResponse({ status: 200, description: '登录成功，返回JWT令牌' })
  @ApiResponse({ status: 401, description: '用户名或密码错误' })
  @Public() // 标记为公开接口，不需要认证
  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() loginDto: LoginDto) {
    return this.authService.login(loginDto.username, loginDto.password);
  }

  @ApiOperation({ summary: 'Web管理系统登录', description: '只允许主账号登录Web管理系统' })
  @ApiResponse({ status: 200, description: '登录成功，返回JWT令牌' })
  @ApiResponse({ status: 401, description: '用户名或密码错误' })
  @ApiResponse({ status: 403, description: '只有主账号可以登录管理系统' })
  @Public() // 标记为公开接口，不需要认证
  @Post('web-login')
  @HttpCode(HttpStatus.OK)
  async webLogin(@Body() loginDto: LoginDto) {
    return this.authService.webLogin(loginDto.username, loginDto.password);
  }
}
