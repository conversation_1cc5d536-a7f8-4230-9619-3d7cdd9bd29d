import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AccountType } from '@prisma/client';

@Injectable()
export class AccountService {
  constructor(private readonly prismaService: PrismaService) {}

  async create(data: {
    userId: string;
    name: string;
    type: AccountType;
  }) {
    return this.prismaService.account.create({
      data,
    });
  }

  async findByUserId(userId: string) {
    return this.prismaService.account.findMany({
      where: { userId },
      include: {
        stores: true,
      },
    });
  }

  async getUserAccountsWithStores(userId: string) {
    // 获取用户信息
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        username: true,
        role: true,
        company: true,
        phone: true,
        email: true,
      },
    });

    if (!user) {
      return null;
    }

    // 获取用户的所有账号及其门店
    const accounts = await this.prismaService.account.findMany({
      where: { userId },
      include: {
        stores: true,
      },
    });

    return {
      user,
      accounts,
    };
  }
}
