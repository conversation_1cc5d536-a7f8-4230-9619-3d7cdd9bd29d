# Ubuntu NVM 安装和使用说明

## 1. 安装 NVM

### 1.1 安装必要的依赖
```bash
sudo apt update
sudo apt install -y curl wget git build-essential
```

### 1.2 下载并执行 NVM 安装脚本
```bash
# 使用 curl 安装
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash

# 或者使用 wget 安装
wget -qO- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash
```

### 1.3 配置环境变量
```bash
# 将以下配置添加到 ~/.bashrc 文件末尾
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

# 重新加载配置
source ~/.bashrc

# 验证安装
nvm --version
```

## 2. 使用 NVM

### 2.1 查看可用的 Node.js 版本
```bash
# 列出所有可用的 LTS 版本
nvm ls-remote --lts

# 列出所有已安装的版本
nvm ls
```

### 2.2 安装 Node.js
```bash
# 安装最新的 LTS 版本
nvm install --lts

# 安装指定版本
nvm install 18.19.0

# 安装最新版本
nvm install node
```

### 2.3 切换 Node.js 版本
```bash
# 使用指定版本
nvm use 18.19.0

# 使用最新的 LTS 版本
nvm use --lts

# 设置默认版本
nvm alias default 18.19.0
```

### 2.4 项目特定版本配置
```bash
# 在项目根目录创建 .nvmrc 文件
echo "18.19.0" > .nvmrc

# 进入项目目录后自动切换到对应版本
cd 项目目录
nvm use
```

## 3. 常用命令

```bash
# 查看当前使用的版本
nvm current

# 查看 Node.js 安装路径
nvm which 18.19.0

# 卸载指定版本
nvm uninstall 18.19.0

# 设置默认版本
nvm alias default 18.19.0

# 运行指定版本的 Node.js
nvm run 18.19.0 app.js

# 在当前终端使用指定版本
nvm exec 18.19.0 node app.js
```

## 4. 最佳实践

### 4.1 项目版本管理
1. 在每个项目根目录创建 `.nvmrc` 文件
2. 在文件中指定项目使用的 Node.js 版本
3. 在项目文档中说明所需的 Node.js 版本

### 4.2 自动切换版本
将以下代码添加到 `~/.bashrc` 或 `~/.zshrc`：
```bash
# 自动切换 Node.js 版本
cdnvm() {
    command cd "$@";
    nvm_path=$(nvm_find_up .nvmrc | tr -d '\n')

    if [[ ! -z $nvm_path ]]; then
        nvm_version=$(cat $nvm_path/.nvmrc)
        nvm use $nvm_version
    elif [[ $(nvm version) != $(nvm version default) ]]; then
        nvm use default
    fi
}
alias cd='cdnvm'
```

## 5. 故障排查

### 5.1 常见问题解决
1. nvm 命令未找到
```bash
# 重新加载终端配置
source ~/.bashrc

# 或重新打开终端
```

2. 下载速度慢
```bash
# 设置淘宝镜像
export NVM_NODEJS_ORG_MIRROR=https://npm.taobao.org/mirrors/node
```

3. 权限问题
```bash
# 修复权限
sudo chown -R $USER:$USER ~/.nvm
```

## 6. 维护建议

1. 定期更新 NVM
```bash
# 更新 NVM 到最新版本
(
  cd "$NVM_DIR"
  git fetch --tags origin
  git checkout `git describe --abbrev=0 --tags --match "v[0-9]*" $(git rev-list --tags --max-count=1)`
) && \. "$NVM_DIR/nvm.sh"
```

2. 定期清理旧版本
```bash
# 删除除默认版本外的所有版本
nvm ls | grep -v "default" | grep -v "node" | xargs -I{} nvm uninstall {}
```

3. 备份 NVM 配置
```bash
# 备份 .nvmrc 文件
cp ~/.nvm/.nvmrc ~/backup/.nvmrc.backup
```

## 7. 参考链接

- [NVM GitHub 仓库](https://github.com/nvm-sh/nvm)
- [Node.js 官方文档](https://nodejs.org/docs)