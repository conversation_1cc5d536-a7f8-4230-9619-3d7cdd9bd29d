-- CreateTable
CREATE TABLE "User" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "username" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "phone" TEXT,
    "email" TEXT,
    "company" TEXT,
    "role" TEXT NOT NULL DEFAULT 'CUSTOMER',
    "extField1" TEXT,
    "extField2" TEXT,
    "extField3" TEXT,
    "extField4" TEXT,
    "extField5" TEXT,
    "extField6" TEXT,
    "extField7" TEXT,
    "extField8" TEXT,
    "extField9" TEXT,
    "extField10" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "Account" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "extField1" TEXT,
    "extField2" TEXT,
    "extField3" TEXT,
    "extField4" TEXT,
    "extField5" TEXT,
    "extField6" TEXT,
    "extField7" TEXT,
    "extField8" TEXT,
    "extField9" TEXT,
    "extField10" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Account_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "Store" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "accountId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "shortName" TEXT,
    "extField1" TEXT,
    "extField2" TEXT,
    "extField3" TEXT,
    "extField4" TEXT,
    "extField5" TEXT,
    "extField6" TEXT,
    "extField7" TEXT,
    "extField8" TEXT,
    "extField9" TEXT,
    "extField10" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Store_accountId_fkey" FOREIGN KEY ("accountId") REFERENCES "Account" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "User_username_key" ON "User"("username");
