import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  UseGuards,
  Request,
  BadRequestException,
  Param,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { PrismaService } from '../prisma/prisma.service';
import { QuotaService } from '../quota/quota.service';
import { ImportService } from '../import/import.service';
import { UserRole, AccountType } from '@prisma/client';

@ApiTags('Web管理系统')
@Controller('web')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class WebController {
  constructor(
    private readonly prisma: PrismaService,
    private readonly quotaService: QuotaService,
    private readonly importService: ImportService,
  ) {}

  /**
   * 获取主账号仪表板数据
   */
  @Get('dashboard')
  @ApiOperation({ summary: '获取主账号仪表板数据' })
  @ApiResponse({ status: 200, description: '返回仪表板数据' })
  async getDashboard(@Request() req: any) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true, parentUserId: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以访问管理系统');
    }

    // 获取配额统计
    const quotaCheck = await this.quotaService.checkQuotaLimits(userId);
    
    // 获取子账号数量
    const subUserCount = await this.prisma.user.count({
      where: { parentUserId: userId },
    });

    return {
      quotaStats: quotaCheck.quotaStats,
      quotaLimits: quotaCheck.quotaLimits,
      isWithinLimit: quotaCheck.isWithinLimit,
      exceededTypes: quotaCheck.exceededTypes,
      subUserCount,
    };
  }

  /**
   * 获取主账号下的所有子账号列表
   */
  @Get('sub-users')
  @ApiOperation({ summary: '获取主账号下的子账号列表' })
  @ApiResponse({ status: 200, description: '返回子账号列表' })
  async getSubUsers(@Request() req: any) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以访问管理系统');
    }

    // 获取子账号列表及其账号和门店信息
    const subUsers = await this.prisma.user.findMany({
      where: { parentUserId: userId },
      select: {
        id: true,
        username: true,
        phone: true,
        email: true,
        company: true,
        role: true,
        createdAt: true,
        accounts: {
          select: {
            id: true,
            name: true,
            type: true,
            createdAt: true,
            stores: {
              select: {
                id: true,
                name: true,
                shortName: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    // 统计每个子账号的数据
    const subUsersWithStats = subUsers.map(subUser => {
      const douyinAccounts = subUser.accounts.filter(acc => acc.type === AccountType.DOUYIN);
      const qingliangAccounts = subUser.accounts.filter(acc => acc.type === AccountType.QINGLIANG);
      
      const douyinStores = douyinAccounts.reduce((sum, acc) => sum + acc.stores.length, 0);
      const qingliangStores = qingliangAccounts.reduce((sum, acc) => sum + acc.stores.length, 0);

      return {
        ...subUser,
        stats: {
          douyinAccountCount: douyinAccounts.length,
          qingliangAccountCount: qingliangAccounts.length,
          douyinStoreCount: douyinStores,
          qingliangStoreCount: qingliangStores,
          totalAccountCount: subUser.accounts.length,
          totalStoreCount: douyinStores + qingliangStores,
        },
      };
    });

    return subUsersWithStats;
  }

  /**
   * 获取指定子账号的详细信息
   */
  @Get('sub-users/:subUserId')
  @ApiOperation({ summary: '获取指定子账号的详细信息' })
  @ApiResponse({ status: 200, description: '返回子账号详细信息' })
  async getSubUserDetails(@Request() req: any, @Param('subUserId') subUserId: string) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以访问管理系统');
    }

    // 验证子账号是否属于当前主账号
    const subUser = await this.prisma.user.findUnique({
      where: { id: subUserId },
      select: { parentUserId: true },
    });

    if (!subUser || subUser.parentUserId !== userId) {
      throw new BadRequestException('子账号不存在或不属于当前主账号');
    }

    // 获取子账号详细信息
    let subUserDetails;
    try {
      subUserDetails = await this.prisma.user.findUnique({
        where: { id: subUserId },
        include: {
          accounts: {
            include: {
              stores: true,
            },
          },
        },
      });
    } catch (error) {
      // 如果是配额字段类型转换错误，尝试修复数据后重试
      if (error.code === 'P2023' && (error.message.includes('Quota') || error.message.includes('totalDouyinAccountQuota'))) {
        console.log(`检测到用户 ${subUserId} 的配额字段数据异常，尝试修复...`);

        // 修复配额字段的无效数据
        await this.quotaService.fixQuotaFieldsData(subUserId);

        // 重新尝试查询
        subUserDetails = await this.prisma.user.findUnique({
          where: { id: subUserId },
          include: {
            accounts: {
              include: {
                stores: true,
              },
            },
          },
        });
      } else {
        throw error;
      }
    }

    if (!subUserDetails) {
      throw new BadRequestException('子账号不存在');
    }

    // 计算统计数据
    const douyinAccounts = subUserDetails.accounts.filter(acc => acc.type === AccountType.DOUYIN);
    const qingliangAccounts = subUserDetails.accounts.filter(acc => acc.type === AccountType.QINGLIANG);

    const douyinStores = douyinAccounts.reduce((sum, acc) => sum + acc.stores.length, 0);
    const qingliangStores = qingliangAccounts.reduce((sum, acc) => sum + acc.stores.length, 0);

    // 返回包含统计信息的数据
    return {
      ...subUserDetails,
      stats: {
        douyinAccountCount: douyinAccounts.length,
        qingliangAccountCount: qingliangAccounts.length,
        douyinStoreCount: douyinStores,
        qingliangStoreCount: qingliangStores,
        totalAccountCount: subUserDetails.accounts.length,
        totalStoreCount: douyinStores + qingliangStores,
      },
    };
  }

  /**
   * 获取所有主账号列表（仅超级管理员可访问）
   */
  @Get('main-accounts')
  @ApiOperation({ summary: '获取所有主账号列表' })
  @ApiResponse({ status: 200, description: '返回主账号列表' })
  async getMainAccounts(@Request() req: any) {
    const userId = req.user.id;

    // 验证是否为超级管理员
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.SUPER_ADMIN) {
      throw new BadRequestException('只有超级管理员可以访问此功能');
    }

    // 获取所有主账号
    let mainAccounts;
    try {
      mainAccounts = await this.prisma.user.findMany({
        where: { role: UserRole.MAIN },
        select: {
          id: true,
          username: true,
          phone: true,
          email: true,
          company: true,
          totalDouyinAccountQuota: true,
          totalQingliangAccountQuota: true,
          totalDouyinStoreQuota: true,
          totalQingliangStoreQuota: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      // 如果是配额字段类型转换错误，先修复所有主账号的数据
      if (error.code === 'P2023' && error.message.includes('Quota')) {
        console.log('检测到主账号配额字段数据异常，批量修复...');

        // 批量修复所有用户的配额字段无效数据
        await this.quotaService.fixQuotaFieldsData();

        // 重新尝试查询
        mainAccounts = await this.prisma.user.findMany({
          where: { role: UserRole.MAIN },
          select: {
            id: true,
            username: true,
            phone: true,
            email: true,
            company: true,
            totalDouyinAccountQuota: true,
            totalQingliangAccountQuota: true,
            totalDouyinStoreQuota: true,
            totalQingliangStoreQuota: true,
            createdAt: true,
          },
          orderBy: { createdAt: 'desc' },
        });
      } else {
        throw error;
      }
    }

    return mainAccounts;
  }

  /**
   * 获取指定主账号的配额信息（仅超级管理员可访问）
   */
  @Get('main-account-quota/:mainAccountId')
  @ApiOperation({ summary: '获取指定主账号的配额信息' })
  @ApiResponse({ status: 200, description: '返回主账号配额信息' })
  async getMainAccountQuota(
    @Request() req: any,
    @Param('mainAccountId') mainAccountId: string,
  ) {
    const userId = req.user.id;

    // 验证是否为超级管理员
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.SUPER_ADMIN) {
      throw new BadRequestException('只有超级管理员可以访问此功能');
    }

    // 验证目标用户是否为主账号
    const mainAccount = await this.prisma.user.findUnique({
      where: { id: mainAccountId },
      select: { role: true },
    });

    if (!mainAccount || mainAccount.role !== UserRole.MAIN) {
      throw new BadRequestException('指定的用户不是主账号');
    }

    // 获取配额统计和限制
    const quotaCheck = await this.quotaService.checkQuotaLimits(mainAccountId);

    // 获取子账号数量
    const subUserCount = await this.prisma.user.count({
      where: { parentUserId: mainAccountId },
    });

    return {
      quotaStats: quotaCheck.quotaStats,
      quotaLimits: quotaCheck.quotaLimits,
      isWithinLimit: quotaCheck.isWithinLimit,
      exceededTypes: quotaCheck.exceededTypes,
      subUserCount,
    };
  }

  /**
   * 更新主账号配额设置
   */
  @Post('quota-settings')
  @ApiOperation({ summary: '更新主账号配额设置' })
  @ApiResponse({ status: 200, description: '配额设置更新成功' })
  async updateQuotaSettings(
    @Request() req: any,
    @Body() quotaSettings: {
      mainAccountId?: string; // 超级管理员可以指定主账号ID
      totalDouyinAccountQuota: number;
      totalQingliangAccountQuota: number;
      totalDouyinStoreQuota: number;
      totalQingliangStoreQuota: number;
    },
  ) {
    const userId = req.user.id;

    // 获取当前用户信息
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user) {
      throw new BadRequestException('用户不存在');
    }

    let targetUserId = userId;

    if (user.role === UserRole.SUPER_ADMIN) {
      // 超级管理员可以为指定的主账号设置配额
      if (!quotaSettings.mainAccountId) {
        throw new BadRequestException('超级管理员必须指定要设置配额的主账号');
      }

      // 验证目标用户是否为主账号
      const targetUser = await this.prisma.user.findUnique({
        where: { id: quotaSettings.mainAccountId },
        select: { role: true },
      });

      if (!targetUser || targetUser.role !== UserRole.MAIN) {
        throw new BadRequestException('指定的用户不是主账号');
      }

      targetUserId = quotaSettings.mainAccountId;
    } else if (user.role === UserRole.MAIN) {
      // 主账号只能为自己设置配额
      if (quotaSettings.mainAccountId && quotaSettings.mainAccountId !== userId) {
        throw new BadRequestException('主账号只能修改自己的配额设置');
      }
    } else {
      throw new BadRequestException('只有超级管理员和主账号可以修改配额设置');
    }

    // 更新配额设置
    const updatedUser = await this.prisma.user.update({
      where: { id: targetUserId },
      data: {
        totalDouyinAccountQuota: quotaSettings.totalDouyinAccountQuota,
        totalQingliangAccountQuota: quotaSettings.totalQingliangAccountQuota,
        totalDouyinStoreQuota: quotaSettings.totalDouyinStoreQuota,
        totalQingliangStoreQuota: quotaSettings.totalQingliangStoreQuota,
      },
      select: {
        id: true,
        username: true,
        totalDouyinAccountQuota: true,
        totalQingliangAccountQuota: true,
        totalDouyinStoreQuota: true,
        totalQingliangStoreQuota: true,
      },
    });

    return {
      message: '配额设置更新成功',
      quotaSettings: updatedUser,
    };
  }

  /**
   * 为指定子账号导入抖音数据
   */
  @Post('import/douyin/:subUserId')
  @ApiOperation({ summary: '为指定子账号导入抖音数据' })
  @ApiResponse({ status: 200, description: '导入成功' })
  @UseInterceptors(FileInterceptor('file'))
  async importDouyinForSubUser(
    @Request() req: any,
    @Param('subUserId') subUserId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { strictMode?: string },
  ) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以进行导入操作');
    }

    // 验证子账号是否属于当前主账号
    const subUser = await this.prisma.user.findUnique({
      where: { id: subUserId },
      select: { parentUserId: true },
    });

    if (!subUser || subUser.parentUserId !== userId) {
      throw new BadRequestException('子账号不存在或不属于当前主账号');
    }

    const strictMode = body.strictMode === 'true';
    
    return await this.importService.importFromExcel(
      file,
      subUserId,
      AccountType.DOUYIN,
      strictMode,
    );
  }

  /**
   * 为指定子账号导入清凉数据
   */
  @Post('import/qingliang/:subUserId')
  @ApiOperation({ summary: '为指定子账号导入清凉数据' })
  @ApiResponse({ status: 200, description: '导入成功' })
  @UseInterceptors(FileInterceptor('file'))
  async importQingliangForSubUser(
    @Request() req: any,
    @Param('subUserId') subUserId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { strictMode?: string },
  ) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以进行导入操作');
    }

    // 验证子账号是否属于当前主账号
    const subUser = await this.prisma.user.findUnique({
      where: { id: subUserId },
      select: { parentUserId: true },
    });

    if (!subUser || subUser.parentUserId !== userId) {
      throw new BadRequestException('子账号不存在或不属于当前主账号');
    }

    const strictMode = body.strictMode === 'true';
    
    return await this.importService.importFromExcel(
      file,
      subUserId,
      AccountType.QINGLIANG,
      strictMode,
    );
  }

  /**
   * 批量导入（同时导入抖音和清凉数据）
   */
  @Post('import/batch/:subUserId')
  @ApiOperation({ summary: '为指定子账号批量导入数据' })
  @ApiResponse({ status: 200, description: '批量导入成功' })
  @UseInterceptors(FileInterceptor('file'))
  async batchImportForSubUser(
    @Request() req: any,
    @Param('subUserId') subUserId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body() body: { strictMode?: string },
  ) {
    const userId = req.user.id;
    
    // 验证是否为主账号
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.MAIN) {
      throw new BadRequestException('只有主账号可以进行导入操作');
    }

    // 验证子账号是否属于当前主账号
    const subUser = await this.prisma.user.findUnique({
      where: { id: subUserId },
      select: { parentUserId: true },
    });

    if (!subUser || subUser.parentUserId !== userId) {
      throw new BadRequestException('子账号不存在或不属于当前主账号');
    }

    const strictMode = body.strictMode === 'true';
    
    try {
      // 先进行整体配额预检查
      const preCheckResult = await this.importService.preCheckBatchImportQuota(
        file,
        subUserId,
        strictMode,
      );
      
      console.log('批量导入预检查通过:', preCheckResult.combinedStats);

      // 先导入抖音数据（跳过配额检查，因为已经预检查过了）
      const douyinResult = await this.importService.importFromExcel(
        file,
        subUserId,
        AccountType.DOUYIN,
        strictMode,
        true, // 跳过配额检查
      );

      // 再导入清凉数据（非严格模式，避免清空已导入的抖音数据，跳过配额检查）
      const qingliangResult = await this.importService.importFromExcel(
        file,
        subUserId,
        AccountType.QINGLIANG,
        false, // 第二次导入使用非严格模式
        true, // 跳过配额检查
      );

      const result = {
        message: '批量导入成功',
        douyin: douyinResult,
        qingliang: qingliangResult,
      };

      return result;
    } catch (error) {
      throw new BadRequestException(`批量导入失败: ${error.message}`);
    }
  }

  /**
   * 清空指定子账号的数据
   */
  @Delete('clear-data/:subUserId')
  @ApiOperation({ summary: '清空指定子账号的数据（仅超级管理员）' })
  @ApiResponse({ status: 200, description: '数据清空成功' })
  async clearSubUserData(
    @Request() req: any,
    @Param('subUserId') subUserId: string,
    @Body() body: { 
      accountType?: 'DOUYIN' | 'QINGLIANG' | 'ALL';
      dataType?: 'accounts' | 'stores' | 'all';
    },
  ) {
    const userId = req.user.id;
    
    // 验证是否为超级管理员
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.SUPER_ADMIN) {
      throw new BadRequestException('只有超级管理员可以进行清空操作');
    }

    // 验证子账号是否存在
    const subUser = await this.prisma.user.findUnique({
      where: { id: subUserId },
      select: { id: true, username: true, role: true },
    });

    if (!subUser) {
      throw new BadRequestException('子账号不存在');
    }

    const accountType = body.accountType || 'ALL';
    const dataType = body.dataType || 'all';
    
    try {
      let result: any = {
        message: '数据清空成功',
        clearedData: {
          accounts: 0,
          stores: 0,
        },
      };

      // 构建账号类型过滤条件
      const accountTypeFilter = accountType === 'ALL' 
        ? {} 
        : { type: accountType as AccountType };

      if (dataType === 'all' || dataType === 'stores') {
        // 清空门店数据
        const storeDeleteResult = await this.prisma.store.deleteMany({
          where: {
            account: {
              userId: subUserId,
              ...accountTypeFilter,
            },
          },
        });
        result.clearedData.stores = storeDeleteResult.count;
      }

      if (dataType === 'all' || dataType === 'accounts') {
        // 清空账号数据（这会级联删除关联的门店）
        const accountDeleteResult = await this.prisma.account.deleteMany({
          where: {
            userId: subUserId,
            ...accountTypeFilter,
          },
        });
        result.clearedData.accounts = accountDeleteResult.count;
      }

      const accountTypeText = accountType === 'ALL' ? '全部' : 
                             accountType === 'DOUYIN' ? '抖店' : '清凉';
      const dataTypeText = dataType === 'all' ? '账号和门店数据' :
                          dataType === 'accounts' ? '账号数据' : '门店数据';

      result.message = `已清空子账号 ${subUser.username} 的 ${accountTypeText} ${dataTypeText}`;

      return result;
    } catch (error) {
      throw new BadRequestException(`数据清空失败: ${error.message}`);
    }
  }

  /**
   * 获取子账号列表（根据主账号）
   */
  @Get('main-account/:mainAccountId/sub-users')
  @ApiOperation({ summary: '获取指定主账号下的子账号列表（仅超级管理员）' })
  @ApiResponse({ status: 200, description: '获取子账号列表成功' })
  async getSubUsersByMainAccount(
    @Request() req: any,
    @Param('mainAccountId') mainAccountId: string,
  ) {
    const userId = req.user.id;
    
    // 验证是否为超级管理员
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { role: true },
    });

    if (!user || user.role !== UserRole.SUPER_ADMIN) {
      throw new BadRequestException('只有超级管理员可以查看子账号列表');
    }

    // 验证主账号是否存在
    const mainAccount = await this.prisma.user.findUnique({
      where: { id: mainAccountId },
      select: { id: true, role: true },
    });

    if (!mainAccount || mainAccount.role !== UserRole.MAIN) {
      throw new BadRequestException('主账号不存在');
    }

    // 获取子账号列表及其数据统计
    const subUsers = await this.prisma.user.findMany({
      where: {
        parentUserId: mainAccountId,
      },
      select: {
        id: true,
        username: true,
        role: true,
        createdAt: true,
        accounts: {
          select: {
            id: true,
            type: true,
            stores: {
              select: {
                id: true,
              },
            },
          },
        },
      },
    });

    // 计算每个子账号的数据统计
    const subUsersWithStats = subUsers.map(subUser => {
      const douyinAccounts = subUser.accounts.filter(acc => acc.type === AccountType.DOUYIN);
      const qingliangAccounts = subUser.accounts.filter(acc => acc.type === AccountType.QINGLIANG);
      
      const douyinStores = douyinAccounts.reduce((sum, acc) => sum + acc.stores.length, 0);
      const qingliangStores = qingliangAccounts.reduce((sum, acc) => sum + acc.stores.length, 0);

      return {
        id: subUser.id,
        username: subUser.username,
        role: subUser.role,
        createdAt: subUser.createdAt,
        stats: {
          douyinAccountCount: douyinAccounts.length,
          qingliangAccountCount: qingliangAccounts.length,
          douyinStoreCount: douyinStores,
          qingliangStoreCount: qingliangStores,
          totalAccountCount: subUser.accounts.length,
          totalStoreCount: douyinStores + qingliangStores,
        },
      };
    });

    return subUsersWithStats;
  }
}