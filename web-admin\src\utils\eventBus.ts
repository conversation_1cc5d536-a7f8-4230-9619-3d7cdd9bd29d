/**
 * 简单的事件总线，用于组件间通信
 */
class EventBus {
  private events: { [key: string]: Function[] } = {};

  /**
   * 订阅事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  on(event: string, callback: Function) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  /**
   * 取消订阅事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  off(event: string, callback: Function) {
    if (!this.events[event]) return;
    
    const index = this.events[event].indexOf(callback);
    if (index > -1) {
      this.events[event].splice(index, 1);
    }
  }

  /**
   * 触发事件
   * @param event 事件名称
   * @param data 事件数据
   */
  emit(event: string, data?: any) {
    if (!this.events[event]) return;
    
    this.events[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`Event callback error for ${event}:`, error);
      }
    });
  }

  /**
   * 清除所有事件监听器
   */
  clear() {
    this.events = {};
  }
}

// 创建全局事件总线实例
export const eventBus = new EventBus();

// 定义事件类型常量
export const EVENTS = {
  DATA_CLEARED: 'data_cleared', // 数据清空事件
  QUOTA_UPDATED: 'quota_updated', // 配额更新事件
  DASHBOARD_REFRESH: 'dashboard_refresh', // 仪表板刷新事件
} as const;
