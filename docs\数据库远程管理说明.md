# SQLite 数据库远程管理说明

## 概述

本文档描述如何通过 Prisma Studio 和 SSH 隧道安全地远程管理项目的 SQLite 数据库。这种方式既安全又便捷，特别适合开发和维护阶段使用。

## 1. 服务器端配置

### 1.1 启动 Prisma Studio

```bash
# 进入项目目录
cd /var/www/api-server

# 使用 PM2 启动 Prisma Studio
pm2 start "npx prisma studio --port 5555 --hostname 127.0.0.1" --name "prisma-studio"
pm2 save

# 检查运行状态
pm2 status
pm2 logs prisma-studio
```

### 1.2 安全配置

```bash
# 设置防火墙规则
sudo ufw allow ssh
sudo ufw enable

# 设置数据库文件权限
sudo chown www-data:www-data /var/db/prod.db
sudo chmod 640 /var/db/prod.db
```

## 2. 本地电脑配置

### 2.1 SSH 隧道配置

在本地电脑的 `~/.ssh/config`（Windows 通常在 `C:\Users\<USER>\.ssh\config`）中添加以下配置：

```text
Host api-server
    HostName your-server-ip
    User your-username
    LocalForward 5555 localhost:5555
    ServerAliveInterval 60
    ServerAliveCountMax 3
```

### 2.2 创建连接脚本

Windows 用户创建 `connect-db.bat`：
```batch
@echo off
echo 正在建立到数据库的安全连接...
ssh -L 5555:localhost:5555 user@your-server-ip
```

Mac/Linux 用户创建 `connect-db.sh`：
```bash
#!/bin/bash
echo "正在建立到数据库的安全连接..."
ssh -L 5555:localhost:5555 user@your-server-ip

# 设置执行权限
chmod +x connect-db.sh
```

## 3. 使用说明

### 3.1 建立连接

1. 运行连接脚本：
   - Windows: 双击运行 `connect-db.bat`
   - Mac/Linux: 在终端运行 `./connect-db.sh`

2. 输入服务器密码

3. 保持终端窗口开启（关闭窗口会断开连接）

### 3.2 访问数据库

1. 打开浏览器
2. 访问 http://localhost:5555
3. 使用 Prisma Studio 图形界面管理数据库

### 3.3 常见操作

- 查看数据表：点击左侧表名
- 添加记录：点击 "Add record" 按钮
- 编辑记录：直接点击相应字段
- 删除记录：点击记录右侧的删除图标
- 筛选数据：使用顶部的过滤器功能
- 排序数据：点击列标题

## 4. 故障排除

### 4.1 连接被拒绝

```bash
# 检查 Prisma Studio 是否运行
pm2 status
pm2 logs prisma-studio

# 检查端口占用
sudo lsof -i :5555

# 重启 Prisma Studio
pm2 restart prisma-studio
```

### 4.2 连接断开

- 关闭现有终端窗口
- 重新运行连接脚本
- 重新登录 Prisma Studio

### 4.3 无法访问数据库

1. 检查 SSH 隧道是否正常：
```bash
# 在本地运行
netstat -an | grep 5555
```

2. 检查服务器端口：
```bash
# 在服务器运行
netstat -an | grep 5555
```

## 5. 安全注意事项

1. 不要将 Prisma Studio 暴露在公网
2. 定期更改服务器密码
3. 建议使用 SSH 密钥认证
4. 定期备份数据库
5. 及时更新系统和依赖包

## 6. 数据库备份

```bash
# 在服务器上执行备份
cd /var/www/api-server
cp prisma/dev.db backup/dev.db.$(date +%Y%m%d)

# 或使用 SQLite 命令导出
sqlite3 prisma/dev.db ".backup 'backup/dev.db.$(date +%Y%m%d)'"
```

## 7. 数据库结构更新

### 7.1 更新流程

1. 本地开发环境操作：
```bash
# 修改 prisma/schema.prisma 文件，更新数据模型

# 生成迁移文件
npx prisma migrate dev --name 迁移说明

# 验证数据结构是否正确
npx prisma studio
```

2. 提交更改到版本控制：
```bash
git add .
git commit -m "更新数据库结构：添加xxx字段"
git push
```

3. 服务器端部署：
```bash
# 1. 备份当前数据库
cd /var/www/api-server
cp prisma/dev.db backup/dev.db.$(date +%Y%m%d)

# 2. 拉取更新
git pull

# 3. 安装依赖（如有新依赖）
npm install

# 4. 应用数据库迁移
npx prisma migrate deploy

# 5. 重新生成 Prisma Client
npx prisma generate

# 6. 重启应用
pm2 restart api-server

# 7. 重启 Prisma Studio
pm2 restart prisma-studio
```

### 7.2 注意事项

1. 破坏性更改
   - 删除字段、修改字段类型等操作需要特别谨慎
   - 建议先在测试环境验证迁移效果
   - 确保有完整的数据备份

2. 迁移验证
   - 迁移后通过 Prisma Studio 检查数据完整性
   - 验证应用功能是否正常
   - 检查日志是否有异常

3. 回滚准备
   - 保存完整的数据库备份
   - 记录回滚所需的操作步骤
   - 准备回滚脚本（如果需要）

## 8. 维护建议

1. 定期检查数据库大小
2. 监控服务器资源使用情况
3. 检查并清理不必要的数据
4. 保持 Prisma Studio 和依赖包更新

## 9. 参考链接

- [Prisma Migrate 文档](https://www.prisma.io/docs/concepts/components/prisma-migrate)
- [Prisma Studio 官方文档](https://www.prisma.io/studio)
- [SSH 隧道说明](https://www.ssh.com/academy/ssh/tunneling)
- [SQLite 官方文档](https://sqlite.org/docs.html)
