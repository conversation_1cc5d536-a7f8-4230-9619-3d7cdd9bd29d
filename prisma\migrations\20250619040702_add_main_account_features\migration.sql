-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_User" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "username" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "phone" TEXT,
    "email" TEXT,
    "company" TEXT,
    "role" TEXT NOT NULL DEFAULT 'CUSTOMER',
    "totalDouyinAccountQuota" INTEGER,
    "totalQingliangAccountQuota" INTEGER,
    "totalDouyinStoreQuota" INTEGER,
    "totalQingliangStoreQuota" INTEGER,
    "parentUserId" TEXT,
    "extField1" TEXT,
    "extField2" TEXT,
    "extField3" TEXT,
    "extField4" TEXT,
    "extField5" TEXT,
    "extField6" TEXT,
    "extField7" TEXT,
    "extField8" TEXT,
    "extField9" TEXT,
    "extField10" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "User_parentUserId_fkey" FOREIGN KEY ("parentUserId") REFERENCES "User" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);
INSERT INTO "new_User" ("company", "createdAt", "email", "extField1", "extField10", "extField2", "extField3", "extField4", "extField5", "extField6", "extField7", "extField8", "extField9", "id", "password", "phone", "role", "updatedAt", "username") SELECT "company", "createdAt", "email", "extField1", "extField10", "extField2", "extField3", "extField4", "extField5", "extField6", "extField7", "extField8", "extField9", "id", "password", "phone", "role", "updatedAt", "username" FROM "User";
DROP TABLE "User";
ALTER TABLE "new_User" RENAME TO "User";
CREATE UNIQUE INDEX "User_username_key" ON "User"("username");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
