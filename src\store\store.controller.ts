import { Controller, Get, Post, Body, Param, UseGuards, Request } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { StoreService } from './store.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { CreateStoreDto } from './dto/create-store.dto';

@ApiTags('门店')
@ApiBearerAuth('access-token')
@Controller('stores')
export class StoreController {
  constructor(private readonly storeService: StoreService) {}

  @ApiOperation({ summary: '创建门店', description: '为指定账号创建新门店' })
  @ApiResponse({ status: 201, description: '门店创建成功' })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 403, description: '无权操作此账号' })
  @UseGuards(JwtAuthGuard)
  @Post()
  create(
    @Body() createStoreDto: CreateStoreDto,
    @Request() req,
  ) {
    return this.storeService.create({
      ...createStoreDto,
      userId: req.user.id,
    });
  }

  @ApiOperation({ summary: '获取账号下的门店列表', description: '获取指定账号下的所有门店' })
  @ApiParam({ name: 'accountId', description: '账号ID' })
  @ApiResponse({ status: 200, description: '成功获取门店列表' })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 403, description: '无权查看此账号的门店' })
  @UseGuards(JwtAuthGuard)
  @Get('by-account/:accountId')
  findByAccountId(@Param('accountId') accountId: string, @Request() req) {
    return this.storeService.findByAccountId(accountId, req.user.id);
  }
}
