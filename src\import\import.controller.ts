import { Controller, Post, UseInterceptors, UploadedFile, UseGuards, Request, Param, Query, BadRequestException } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth, ApiConsumes, ApiOperation, ApiParam, ApiQuery, ApiResponse, ApiTags, ApiBody } from '@nestjs/swagger';
import { ImportService } from './import.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Express } from 'express';
import { AccountType } from '@prisma/client';

@ApiTags('导入')
@ApiBearerAuth('access-token')
@Controller('import')
export class ImportController {
  constructor(private readonly importService: ImportService) {}

  @ApiOperation({ summary: '导入门店和账号数据', description: '从Excel文件导入门店和账号数据' })
  @ApiConsumes('multipart/form-data')
  @ApiQuery({
    name: 'strictMode',
    required: false,
    type: 'boolean',
    description: '严格模式，默认为true。严格模式下会先清空用户的所有账号和门店，然后重新导入',
    example: true
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Excel文件',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: '导入成功' })
  @ApiResponse({ status: 400, description: '导入失败' })
  @ApiResponse({ status: 401, description: '未授权' })
  @UseGuards(JwtAuthGuard)
  @Post()
  @UseInterceptors(FileInterceptor('file'))
  async importExcel(
    @UploadedFile() file: Express.Multer.File,
    @Query('strictMode') strictMode: string = 'true',
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('未提供文件');
    }

    const isStrictMode = strictMode === 'true';
    return this.importService.importFromExcel(file, req.user.id, AccountType.DOUYIN, isStrictMode);
  }

  @ApiOperation({ summary: '为指定用户导入门店和账号数据', description: '从Excel文件为指定用户导入门店和账号数据（仅超级管理员可用）' })
  @ApiConsumes('multipart/form-data')
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiQuery({
    name: 'strictMode',
    required: false,
    type: 'boolean',
    description: '严格模式，默认为true。严格模式下会先清空用户的所有账号和门店，然后重新导入',
    example: true
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Excel文件',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: '导入成功' })
  @ApiResponse({ status: 400, description: '导入失败' })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 403, description: '无权操作' })
  @UseGuards(JwtAuthGuard)
  @Post('for-user/:userId')
  @UseInterceptors(FileInterceptor('file'))
  async importExcelForUser(
    @UploadedFile() file: Express.Multer.File,
    @Param('userId') userId: string,
    @Query('strictMode') strictMode: string = 'true',
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('未提供文件');
    }

    // 检查当前用户是否为超级管理员
    if (req.user.role !== 'SUPER_ADMIN') {
      throw new BadRequestException('只有超级管理员可以为其他用户导入数据');
    }

    const isStrictMode = strictMode === 'true';
    return this.importService.importFromExcel(file, userId, AccountType.DOUYIN, isStrictMode);
  }

  @ApiOperation({ summary: '导入轻量账号和门店数据', description: '从Excel文件导入轻量账号和门店数据' })
  @ApiConsumes('multipart/form-data')
  @ApiQuery({
    name: 'strictMode',
    required: false,
    type: 'boolean',
    description: '严格模式，默认为true。严格模式下会先清空用户的所有账号和门店，然后重新导入',
    example: true
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Excel文件',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: '导入成功' })
  @ApiResponse({ status: 400, description: '导入失败' })
  @ApiResponse({ status: 401, description: '未授权' })
  @UseGuards(JwtAuthGuard)
  @Post('qingliang')
  @UseInterceptors(FileInterceptor('file'))
  async importQingliangExcel(
    @UploadedFile() file: Express.Multer.File,
    @Query('strictMode') strictMode: string = 'true',
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('未提供文件');
    }

    const isStrictMode = strictMode === 'true';
    return this.importService.importFromExcel(file, req.user.id, AccountType.QINGLIANG, isStrictMode);
  }

  @ApiOperation({ summary: '为指定用户导入轻量账号和门店数据', description: '从Excel文件为指定用户导入轻量账号和门店数据（仅超级管理员可用）' })
  @ApiConsumes('multipart/form-data')
  @ApiParam({ name: 'userId', description: '用户ID' })
  @ApiQuery({
    name: 'strictMode',
    required: false,
    type: 'boolean',
    description: '严格模式，默认为true。严格模式下会先清空用户的所有账号和门店，然后重新导入',
    example: true
  })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'Excel文件',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: '导入成功' })
  @ApiResponse({ status: 400, description: '导入失败' })
  @ApiResponse({ status: 401, description: '未授权' })
  @ApiResponse({ status: 403, description: '无权操作' })
  @UseGuards(JwtAuthGuard)
  @Post('qingliang/for-user/:userId')
  @UseInterceptors(FileInterceptor('file'))
  async importQingliangExcelForUser(
    @UploadedFile() file: Express.Multer.File,
    @Param('userId') userId: string,
    @Query('strictMode') strictMode: string = 'true',
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('未提供文件');
    }

    // 检查当前用户是否为超级管理员
    if (req.user.role !== 'SUPER_ADMIN') {
      throw new BadRequestException('只有超级管理员可以为其他用户导入数据');
    }

    const isStrictMode = strictMode === 'true';
    return this.importService.importFromExcel(file, userId, AccountType.QINGLIANG, isStrictMode);
  }
}
