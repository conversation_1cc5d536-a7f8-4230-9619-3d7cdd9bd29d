import { Injectable, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import * as crypto from 'crypto';
import { User, UserRole } from '@prisma/client';

@Injectable()
export class UserService {
  constructor(private readonly prismaService: PrismaService) {}

  // MD5加密
  private md5(text: string): string {
    return crypto.createHash('md5').update(text).digest('hex');
  }

  async create(data: {
    username: string;
    password: string;
    phone?: string;
    email?: string;
    company?: string;
    role?: UserRole;
  }): Promise<Omit<User, 'password'>> {
    // 检查用户名是否已存在
    const existingUser = await this.prismaService.user.findUnique({
      where: { username: data.username },
    });

    if (existingUser) {
      throw new ConflictException('用户名已存在');
    }

    // 创建新用户
    const user = await this.prismaService.user.create({
      data: {
        ...data,
        password: this.md5(data.password),
      },
    });

    // 不返回密码
    const { password, ...result } = user;
    return result;
  }

  async findById(id: string): Promise<Omit<User, 'password'> | null> {
    const user = await this.prismaService.user.findUnique({
      where: { id },
    });

    if (!user) {
      return null;
    }

    // 不返回密码
    const { password, ...result } = user;
    return result;
  }
}
