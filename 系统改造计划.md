## 任务背景
目前系统需要按照以下两大块【接口及数据层数据库升级】【界面升级】进行升级，请仔细阅读，规划好任务计划，然后分步骤迭代进行完成。
  
## 【接口及数据层数据库升级】
* 1. 账号体系升级，新增一个主账号角色，主账号里面配置总抖店账号绑定数量配额、总清凉订单账号绑定数量配额、总抖店门店绑定数量配额、总清凉门店绑定数量配额，对应的User用户表增加这四个字段，然后UserRole里面增加MAIN作为主账号的新角色；
* 2. 系统增加一个方法类，里面包含相关角色额度查询判断方法，可以实时查询当前登录主账号下，所有子账号加上主账号绑定的 抖店门店绑定数量（Account表的 AccountType 为DOUYIN），以及对应的门店（Store表）的总数量，然后 清凉类型的也是一样通用查询方法。然后再给出几个判断方法，判断当前门店是否已超出对应的配额。
* 3. 在数据导入方法里面（src\import 下的导入接口），需要判断导入的内容是否以及超出限额，如果超出了，则不允许导入，这里注意的是判断是否超出的依据是 准备导入的数量加上数据库的数量是否超限额，如果超出了则不允许本次导入，当然如果是用目前导入里面写的严格模式，则直接判断当前即将导入的是否超过限额即可，但注意这个判断需要在清空账号之前判断，不然会出现导入失败但账号被清空的情况，影响原来导入前的使用；
* 4. User用户表增加一个所属主账号字段，关联主账号的用户uuid，这样就可以形成主账号 子账号的关系了。
* 
## 【界面升级】
* 1. 新增一个管理系统，我的想法是直接在当前的环境下面增加一个web 站点，在项目启动的时候可以通过相同的后端路径 /web/xxx 来访问管理界面；
* 2. 管理系统的功能有，登录功能，可以通过主账号进行登录，登录后可以看到主账号下所属的子账号列表，以及子账号下绑定的抖店等相关账号以及门店信息；
* 3. 登录进去后会有比较醒目的限额配置以及当前用量展示；
* 4. 有一个导入功能，可以调用 src\import 下的导入接口，当然了接口调用是用前后端分离的模式进行；
* 5. 目前系统限制只能主账号进行登录，其他类型的账号登录不了；
* 6. 导入功能目前直接在前端做个合并，就是接口目前分了导入抖店和清凉分开的，实际上我们直接一次导入操作就直接调用这两个接口进行导入了，导入直接选择对应的子账号进行导入；
* 7. 请分析当前的需求，需要补充的接口就直接加就好；
* 8. 前端技术栈就使用 react + antd 来实现即可，我理解是实现完后，直接build到当前一个相对目录，然后因为目前是pm2部署到线上，是不是直接相同的后台地址 /web/xxx 来访问即可？