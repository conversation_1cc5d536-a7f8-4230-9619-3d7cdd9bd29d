import api from './api';

export interface LoginParams {
  username: string;
  password: string;
}

export interface User {
  id: string;
  username: string;
  phone?: string;
  email?: string;
  company?: string;
  role: string;
}

export interface LoginResponse {
  access_token: string;
  user: User;
}

export const authService = {
  // Web管理系统登录
  login: async (params: LoginParams): Promise<LoginResponse> => {
    const response = await api.post('/auth/web-login', params);
    return response.data;
  },

  // 获取当前用户信息
  getCurrentUser: (): User | null => {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  // 获取token
  getToken: (): string | null => {
    return localStorage.getItem('token');
  },

  // 设置认证信息
  setAuth: (token: string, user: User) => {
    localStorage.setItem('token', token);
    localStorage.setItem('user', JSON.stringify(user));
  },

  // 清除认证信息
  clearAuth: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  // 检查是否已登录
  isAuthenticated: (): boolean => {
    return !!localStorage.getItem('token');
  },
};