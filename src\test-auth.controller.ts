import { Controller, Get, UseGuards, Request } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from './auth/jwt-auth.guard';

@ApiTags('测试认证')
@ApiBearerAuth('access-token')
@Controller('test-auth')
export class TestAuthController {
  @ApiOperation({ summary: '测试JWT认证', description: '测试JWT认证是否正常工作' })
  @ApiResponse({ status: 200, description: '认证成功' })
  @ApiResponse({ status: 401, description: '未授权' })
  @UseGuards(JwtAuthGuard)
  @Get()
  test(@Request() req) {
    // 打印请求头信息
    console.log('测试认证 - 请求头:', req.headers);
    console.log('测试认证 - 用户信息:', req.user);
    
    return {
      message: '认证成功',
      user: req.user,
      headers: {
        authorization: req.headers.authorization
      }
    };
  }
}
